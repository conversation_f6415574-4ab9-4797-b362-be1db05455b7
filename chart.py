"""
AntV G2 Chart Generator for Python using Playwright (Pure Python Implementation)

This module provides functionality to generate charts using AntV G2 and Playwright,
returning them as base64 encoded images. This version uses pure Python without Node.js.

Dependencies:
- Python packages: pandas, playwright, asyncio
- Playwright browsers: chromium

Installation:
1. Install Python dependencies: pip install pandas playwright
2. Install Playwright browsers: playwright install chromium

Usage:
    from chart_playwright import generate_chart

    data = [
        {"category": "A", "value": 10},
        {"category": "B", "value": 20},
        {"category": "C", "value": 15}
    ]

    base64_image = generate_chart(data, "bar", {"title": "Sample Chart"})
"""

import json
import base64
import asyncio
import re
from typing import Dict, List, Union, Optional, Tuple
import pandas as pd
from playwright.async_api import async_playwright
# from .logger import logger


class ChartGeneratorError(Exception):
    """Custom exception for chart generation errors"""
    pass


def validate_data(data_source: Union[pd.DataFrame, List[Dict], Dict]) -> List[Dict]:
    """
    Validate and convert data source to a standardized format.
    
    Args:
        data_source: Input data in various formats
        
    Returns:
        List of dictionaries representing the data
        
    Raises:
        ChartGeneratorError: If data format is invalid or empty
    """
    if data_source is None:
        raise ChartGeneratorError("Data source cannot be None")
    
    if isinstance(data_source, pd.DataFrame):
        if data_source.empty:
            raise ChartGeneratorError("DataFrame is empty")
        return data_source.to_dict('records')
    
    elif isinstance(data_source, list):
        if not data_source:
            raise ChartGeneratorError("Data list is empty")
        if not all(isinstance(item, dict) for item in data_source):
            raise ChartGeneratorError("All items in data list must be dictionaries")
        return data_source
    
    elif isinstance(data_source, dict):
        if not data_source:
            raise ChartGeneratorError("Data dictionary is empty")
        # Convert dict to list of dicts format
        if all(isinstance(v, (list, tuple)) for v in data_source.values()):
            # Assume it's in format {"x": [1,2,3], "y": [4,5,6]}
            keys = list(data_source.keys())
            length = len(data_source[keys[0]])
            return [{k: data_source[k][i] for k in keys} for i in range(length)]
        else:
            # Single record
            return [data_source]
    
    else:
        raise ChartGeneratorError(f"Unsupported data format: {type(data_source)}")


def detect_value_format(value: str) -> str:
    """
    检测数值的格式类型

    Args:
        value: 字符串形式的数值

    Returns:
        格式类型: 'percentage', 'thousands', 'decimal', 'integer'
    """
    if not isinstance(value, str):
        return 'number'

    value = value.strip()

    # 检测百分比
    if value.endswith('%'):
        return 'percentage'

    # 检测千分位分隔符
    if ',' in value and re.match(r'^-?\d{1,3}(,\d{3})*(\.\d+)?$', value):
        return 'thousands'

    # 检测小数
    if '.' in value and re.match(r'^-?\d+\.\d+$', value):
        return 'decimal'

    # 检测整数
    if re.match(r'^-?\d+$', value):
        return 'integer'

    return 'unknown'


def convert_formatted_value(value) -> Tuple[float, str]:
    """
    将格式化的数值转换为纯数值，并返回原始格式类型

    Args:
        value: 待转换的值

    Returns:
        Tuple[float, str]: (转换后的数值, 格式类型)
    """
    if pd.isna(value) or value is None:
        return 0.0, 'number'

    if isinstance(value, (int, float)):
        return float(value), 'number'

    # 转换为字符串并去除空格
    str_value = str(value).strip()
    format_type = detect_value_format(str_value)

    # 处理百分比格式
    if format_type == 'percentage':
        try:
            # 移除百分号并转换为小数
            numeric_part = str_value[:-1].replace(',', '')
            return float(numeric_part) / 100.0, 'percentage'
        except ValueError:
            return 0.0, 'percentage'

    # 处理千分位分隔符
    elif format_type == 'thousands':
        try:
            # 移除千分位逗号
            cleaned_value = str_value.replace(',', '')
            return float(cleaned_value), 'thousands'
        except ValueError:
            return 0.0, 'thousands'

    # 处理其他数值格式
    else:
        try:
            return float(str_value), format_type
        except ValueError:
            # 如果转换失败，尝试提取数字
            numbers = re.findall(r'-?\d+\.?\d*', str_value)
            if numbers:
                return float(numbers[0]), 'unknown'
            return 0.0, 'unknown'


def create_formatter_function(format_type: str) -> str:
    """
    根据格式类型创建JavaScript格式化函数字符串

    Args:
        format_type: 格式类型

    Returns:
        JavaScript格式化函数字符串
    """
    if format_type == 'percentage':
        return '.2%'
    elif format_type == 'thousands':
        return ',.0f'
    elif format_type == 'decimal':
        return '.2f'
    elif format_type == 'integer':
        return '.0f'
    else:
        return ''


def preprocess_chart_data(data: List[Dict], config: Dict) -> Tuple[List[Dict], Dict]:
    """
    预处理图表数据，转换格式化数值并检测格式类型

    Args:
        data: 原始数据
        config: 图表配置

    Returns:
        Tuple[List[Dict], Dict]: (处理后的数据, 格式信息)
    """
    if not data:
        return data, {}

    processed_data = []
    format_info = {}

    # 获取数值字段
    y_field = config.get("y_field", list(data[0].keys())[1] if len(data[0].keys()) > 1 else None)
    value_field = config.get("value_field", y_field)

    # 检测数值字段的格式
    numeric_fields = []
    if y_field:
        numeric_fields.append(y_field)
    if value_field and value_field != y_field:
        numeric_fields.append(value_field)

    # 分析每个数值字段的格式
    for field in numeric_fields:
        if field in data[0]:
            # 取第一个非空值来检测格式
            sample_value = None
            for row in data:
                if field in row and row[field] is not None and str(row[field]).strip():
                    sample_value = row[field]
                    break

            if sample_value is not None:
                _, format_type = convert_formatted_value(sample_value)
                format_info[field] = format_type

    # 转换数据
    for row in data:
        processed_row = {}
        for key, value in row.items():
            if key in numeric_fields:
                converted_value, _ = convert_formatted_value(value)
                processed_row[key] = converted_value
            else:
                processed_row[key] = value
        processed_data.append(processed_row)

    return processed_data, format_info


def validate_chart_type(chart_type: str) -> str:
    """
    Validate chart type.
    
    Args:
        chart_type: Type of chart to generate
        
    Returns:
        Validated chart type
        
    Raises:
        ChartGeneratorError: If chart type is not supported
    """
    supported_types = ["line", "bar", "pie", "scatter"]
    if chart_type not in supported_types:
        raise ChartGeneratorError(f"Unsupported chart type: {chart_type}. Supported types: {supported_types}")
    return chart_type


def create_g2_config(data: List[Dict], chart_type: str, config: Dict, format_info: Dict = None) -> Dict:
    """
    Create G2 chart configuration based on data and chart type.

    Args:
        data: Validated data
        chart_type: Type of chart
        config: Additional configuration options
        format_info: Format information for numeric fields

    Returns:
        G2 chart configuration dictionary
    """
    # Default configuration
    default_config = {
        "width": config.get("width", 800),
        "height": config.get("height", 600),
        "title": config.get("title", ""),
    }
    
    # Base chart configuration
    chart_config = {
        "type": "view",
        "data": data,
        "width": default_config["width"],
        "height": default_config["height"],
        "paddingTop": 50,
        "paddingRight": 50
    }
    
    if default_config["title"]:
        chart_config["title"] = {
            "title": default_config["title"],
            # "style": {"fontSize": 16, "fontWeight": "bold"}
        }

    # Initialize format_info if not provided
    if format_info is None:
        format_info = {}

    # Helper function to get formatter for a field
    def get_field_formatter(field_name: str) -> str:
        field_format = format_info.get(field_name, 'number')
        return create_formatter_function(field_format)

    # Chart type specific configurations
    if chart_type == "line":
        x_field = config.get("x_field", list(data[0].keys())[0])
        y_field = config.get("y_field", list(data[0].keys())[1])

        chart_config["children"] = [{
            "type": "line",
            "encode": {
                "x": x_field,
                "y": y_field,
                "color": config.get("color_field", None)
            },
            "style": {
                "stroke": config.get("line_color", "#1890ff"),
                "lineWidth": config.get("line_width", 2)
            },
            "labels": [{
              "dx": -10,
              "dy": -12,
              "text": y_field,
              "formatter": get_field_formatter(y_field),
              "transform": [{ "type": "overlapHide" }]
            }]
        }]

        # Configure axis with formatters and titles
        axis_config = {}

        # X axis configuration
        x_axis_config = {
          "labelAutoHide": True
        }
        if x_field in format_info:
            x_axis_config["labelFormatter"] = get_field_formatter(x_field)

        # Add custom X axis title if provided
        x_axis_title = config.get("x_axis_title") or config.get("x_title")
        if x_axis_title:
            x_axis_config["title"] = x_axis_title

        if x_axis_config:
            axis_config["x"] = x_axis_config

        # Y axis configuration
        y_axis_config = {
          "labelAutoHide": True
        }
        if y_field in format_info:
            y_axis_config["labelFormatter"] = get_field_formatter(y_field)

        # Add custom Y axis title if provided
        y_axis_title = config.get("y_axis_title") or config.get("y_title")
        if y_axis_title:
            y_axis_config["title"] = y_axis_title

        if y_axis_config:
            axis_config["y"] = y_axis_config

        # Apply axis configuration if any
        if axis_config:
            chart_config["axis"] = axis_config
            
        # 找到data中value的最小值
        min_value = min([row[y_field] for row in data])
        max_value = max([row[y_field] for row in data])
        chart_config['scale'] = {
          "y": {
            "type": "linear",
            "domain": [min_value * 0.995, max_value * 1.001]
          }
        }
    
    elif chart_type == "bar":
        x_field = config.get("x_field", list(data[0].keys())[0])
        y_field = config.get("y_field", list(data[0].keys())[1])

        chart_config["children"] = [{
            "type": "interval",
            "encode": {
                "x": x_field,
                "y": y_field,
                "color": config.get("color_field", None)
            },
            "style": {
                "fill": config.get("bar_color", "#1890ff")
            },
            "labels": [{"text": y_field}]
        }]

        # Configure axis with formatters and titles
        axis_config = {}

        # X axis configuration
        x_axis_config = {}
        if x_field in format_info:
            x_axis_config["labelFormatter"] = get_field_formatter(x_field)

        # Add custom X axis title if provided
        x_axis_title = config.get("x_axis_title") or config.get("x_title")
        if x_axis_title:
            x_axis_config["title"] = x_axis_title

        if x_axis_config:
            axis_config["x"] = x_axis_config

        # Y axis configuration
        y_axis_config = {}
        if y_field in format_info:
            y_axis_config["labelFormatter"] = get_field_formatter(y_field)

        # Add custom Y axis title if provided
        y_axis_title = config.get("y_axis_title") or config.get("y_title")
        if y_axis_title:
            y_axis_config["title"] = y_axis_title

        if y_axis_config:
            axis_config["y"] = y_axis_config

        # Apply axis configuration if any
        if axis_config:
            chart_config["axis"] = axis_config
    
    elif chart_type == "pie":
        value_field = config.get("value_field", list(data[0].keys())[1])
        category_field = config.get("category_field", list(data[0].keys())[0])

        pie_child = {
            "type": "interval",
            "coordinate": {"type": "theta", "outerRadius": 0.8},
            "encode": {
                "y": value_field,
                "color": category_field
            },
            "labels": [
                {"text": category_field},
                {"text": value_field}
            ]
        }

        # Add label formatter for pie chart if format info is available
        if value_field in format_info:
            pie_child["labels"][1]["formatter"] = get_field_formatter(value_field)

        chart_config["children"] = [pie_child]
    
    elif chart_type == "scatter":
        x_field = config.get("x_field", list(data[0].keys())[0])
        y_field = config.get("y_field", list(data[0].keys())[1])

        chart_config["children"] = [{
            "type": "point",
            "encode": {
                "x": x_field,
                "y": y_field,
                "color": config.get("color_field", None),
                "size": config.get("size_field", None)
            },
            "style": {
                "fill": config.get("point_color", "#1890ff"),
                "r": config.get("point_size", 4)
            },
            "labels": [{"text": y_field}]
        }]

        # Configure axis with formatters and titles
        axis_config = {}

        # X axis configuration
        x_axis_config = {}
        if x_field in format_info:
            x_axis_config["labelFormatter"] = get_field_formatter(x_field)

        # Add custom X axis title if provided
        x_axis_title = config.get("x_axis_title") or config.get("x_title")
        if x_axis_title:
            x_axis_config["title"] = x_axis_title

        if x_axis_config:
            axis_config["x"] = x_axis_config

        # Y axis configuration
        y_axis_config = {}
        if y_field in format_info:
            y_axis_config["labelFormatter"] = get_field_formatter(y_field)

        # Add custom Y axis title if provided
        y_axis_title = config.get("y_axis_title") or config.get("y_title")
        if y_axis_title:
            y_axis_config["title"] = y_axis_title

        if y_axis_config:
            axis_config["y"] = y_axis_config

        # Apply axis configuration if any
        if axis_config:
            chart_config["axis"] = axis_config

    return chart_config


async def _generate_chart_async(
    data: List[Dict],
    chart_type: str,
    config: Dict,
    format_info: Dict = None
) -> str:
    """
    Async function to generate chart using Playwright.
    """
    g2_config = create_g2_config(data, chart_type, config, format_info)
    print(f"g2_config: {g2_config}")

    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        )

        try:
            # Create context and page
            context = await browser.new_context(
                viewport={'width': g2_config['width'] + 100, 'height': g2_config['height'] + 100},
                device_scale_factor=1
            )

            page = await context.new_page()
            page.set_default_timeout(30000)

            # Create HTML content with G2 chart
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <script src="https://unpkg.com/@antv/g2@5.3.4/dist/g2.min.js"></script>
                <style>
                    body {{
                        margin: 0;
                        padding: 20px;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        background-color: white;
                    }}
                    #chart {{
                        width: {g2_config['width']}px;
                        height: {g2_config['height']}px;
                        padding: 0 20px;
                        border: 1px solid #f0f0f0;
                        background-color: white;
                    }}
                    .chart-loading {{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        color: #666;
                    }}
                </style>
            </head>
            <body>
                <div id="chart">
                    <div class="chart-loading">Loading chart...</div>
                </div>
                <script>
                    console.log('Starting chart generation...');

                    try {{
                        const config = {json.dumps(g2_config)};
                        console.log('Chart config:', config);

                        // Clear loading message
                        document.getElementById('chart').innerHTML = '';

                        // Create chart
                        const chart = new G2.Chart({{
                            container: 'chart',
                            ...config
                        }});

                        console.log('Chart created, rendering...');
                        chart.render();

                        // Wait a bit for rendering to complete
                        setTimeout(() => {{
                            console.log('Chart rendering completed');
                            window.chartReady = true;
                        }}, 1000);

                    }} catch (error) {{
                        console.error('Chart rendering error:', error);
                        window.chartError = error.message;

                        // Show error in the chart area
                        document.getElementById('chart').innerHTML =
                            '<div class="chart-loading" style="color: red;">Chart Error: ' + error.message + '</div>';
                    }}
                </script>
            </body>
            </html>"""

            # Set content and wait for load
            await page.set_content(html_content, wait_until='networkidle')

            # Wait for chart to be ready or error
            try:
                await page.wait_for_function(
                    "() => window.chartReady || window.chartError",
                    timeout=20000
                )
            except Exception:
                raise ChartGeneratorError('Chart rendering timed out after 20 seconds')

            # Check for errors
            chart_error = await page.evaluate("() => window.chartError")
            if chart_error:
                raise ChartGeneratorError(f"Chart rendering failed: {chart_error}")

            # Wait a bit more to ensure all animations are complete
            await page.wait_for_timeout(500)

            # Take screenshot of the chart element
            chart_element = page.locator('#chart')
            if not await chart_element.count():
                raise ChartGeneratorError('Chart element not found')

            # Take screenshot
            screenshot_bytes = await chart_element.screenshot(
                type='png',
                omit_background=False,
                animations='disabled'
            )

            # Convert to base64
            base64_string = base64.b64encode(screenshot_bytes).decode('utf-8')
            return base64_string

        finally:
            await browser.close()


def generate_chart(
    data_source: Union[pd.DataFrame, List[Dict], Dict],
    chart_type: str,
    chart_config: Optional[Dict] = None
) -> str:
    """
    Generate a chart using AntV G2 and Playwright, return it as a base64 encoded image.

    Args:
        data_source: Input data (DataFrame, list of dicts, or dict)
        chart_type: Type of chart ("line", "bar", "pie", "scatter")
        chart_config: Optional configuration dictionary

    Returns:
        Base64 encoded image string (PNG format)

    Raises:
        ChartGeneratorError: If chart generation fails
    """
    try:
        # Validate inputs
        data = validate_data(data_source)
        chart_type = validate_chart_type(chart_type)
        config = chart_config or {}

        # Preprocess data to handle formatted values
        processed_data, format_info = preprocess_chart_data(data, config)

        # Run async chart generation with format information
        return asyncio.run(_generate_chart_async(processed_data, chart_type, config, format_info))

    except Exception as e:
        if isinstance(e, ChartGeneratorError):
            raise
        else:
            raise ChartGeneratorError(f"Unexpected error during chart generation: {str(e)}")
