# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import time
import http.client
import json
import openpyxl
import socket
import subprocess
from xbot import print, sleep 
from . import package
from .package import variables as glv   


def _get_column_letter(col_idx):
    """Convert a column number into a column letter (3 -> 'C')

    Right shift the column col_idx by 26 to find column letters in reverse
    order.  These numbers are 1-based, and can be converted to ASCII
    ordinals by adding 64.

    """
    # these indicies corrospond to A -> ZZZ and include all allowed
    # columns
    if not 1 <= col_idx <= 18278:
        raise ValueError("Invalid column index {0}".format(col_idx))
    letters = []
    while col_idx > 0:
        col_idx, remainder = divmod(col_idx, 26)
        # check for exact division and borrow if needed
        if remainder == 0:
            remainder = 26
            col_idx -= 1
        letters.append(chr(remainder + 64))
    return "".join(reversed(letters))


_COL_STRING_CACHE = {}
_STRING_COL_CACHE = {}
for i in range(1, 18279):
    col = _get_column_letter(i)
    _STRING_COL_CACHE[i] = col
    _COL_STRING_CACHE[col] = i


def get_column_letter(idx, ):
    """Convert a column index into a column letter
    (3 -> 'C')
    """
    try:
        return _STRING_COL_CACHE[idx]
    except KeyError:
        raise ValueError("Invalid column index {0}".format(idx))


def column_index_from_string(str_col):
    """Convert a column name into a numerical index
    ('A' -> 1)
    """
    # we use a function argument to get indexed name lookup
    try:
        return _COL_STRING_CACHE[str_col.upper()]
    except KeyError:
        raise ValueError("{0} is not a valid column name".format(str_col))

def ping(host):
    # Use the ping command, sending 4 packets
    command = ["ping", "-c", "4", host]

    try:
        output = subprocess.check_output(command)
        return True
    except subprocess.CalledProcessError as e:
        return False

class Client:
    def __init__(self,
                 app_id,
                 app_secret,
                 is_tenant=False,
                 host="open.feishu.cn") -> None:
        self.app_id = app_id
        self.app_secret = app_secret
        self.is_tenant = is_tenant
        self.host = host
        self.token = self.get_token()

    def get_token(self):
        """gen token"""

        if self.is_tenant:
            api = "/open-apis/auth/v3/tenant_access_token/internal"
        else:
            api = "/open-apis/auth/v3/app_access_token/internal"
        payload = json.dumps({
            "app_id": self.app_id,
            "app_secret": self.app_secret
        })
        headers = {"content-type": "application/json; charset=utf-8"}
        conn = http.client.HTTPSConnection(self.host)
        conn.request("POST", api, payload, headers)
        resp = json.loads(conn.getresponse().read())
        if resp.get("code") == 10003:
            raise Exception("请检查参数")
        #设置token到期时间
        self.expire = resp.get("expire")
        self.deadline = time.time() + self.expire -1700

        if self.is_tenant:
            token = resp.get("tenant_access_token")
        else:
            token = resp.get("app_access_token")
        assert token != None, "请检查 app_id app_secret 是否正确"
        return token

    def validate_token(func):
        def inner(self, *args, **kwargs):
            try:
                res = func(self, *args, **kwargs)
            except Exception as e:
                self.token = self.get_token()
                res = func(self, *args, **kwargs)
            return res

        return inner

    def request(self, method, api, payload={}, content_type="json"):
        error = ""
        
        if content_type == "json":
            payload = json.dumps(payload)
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json; charset=utf-8",
            }
        elif content_type == "multipart":
            # payload应该是已经构建好的multipart数据和boundary
            multipart_data, boundary = payload
            payload = multipart_data
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": f"multipart/form-data; boundary={boundary}",
            }
        elif content_type.startswith("multipart/form-data"):
            # 使用 MultipartEncoder 的情况
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": content_type,
            }
            # payload 已经是 MultipartEncoder 的字符串数据
        else:
            raise ValueError("不支持的content_type")
        
        for i in range(10):
            try:
                conn = http.client.HTTPSConnection(self.host,
                                                   port=443,
                                                   timeout=20)
                self.token = self.get_token()
                conn.request(method, api, payload, headers)
                res = conn.getresponse().read()
                data = json.loads(res)
                url = "https://open.feishu.cn/document/ukTMukTMukTM/ugjM14COyUjL4ITN "
                # 根据时间选择是否要更新 token
                if time.time() > self.deadline:
                    self.token = self.get_token()
                assert data.get(
                    "code"
                ) == 0, f'错误码: {str(data.get("code"))}; 错误信息: {data.get("msg")}; 检查错误代码网址: {url}; 响应结果: {str(data)}'
                conn.close()
                return data

            except socket.timeout:
                xbot.logging.info("请求超时")
                is_connected = ping("baidu.com")
                if not is_connected:
                    xbot.logging.info("网络异常, ping 百度 失败")

            except Exception as e:
                xbot.logging.info(e.args)
                error = e

            sleep(2 * i)
            xbot.logging.info(f"请求异常, 正在进行{i}次重试")
        print(f"pyload: {payload}")
        print(f"path: {api}")
        raise Exception(error)

    def test(self):
        if time.time() > self.deadline:
            self.token = self.get_token()
            print(self.token)
            return True
        return False

class SheetClient(Client):
    def __init__(self, app_id: str, app_secret: str,
                 timeout:int = 2000) -> Client:
        super().__init__(app_id, app_secret, timeout)
        self.api = f"/open-apis/sheets/v2/spreadsheets"
        self.createApi = f"/open-apis/sheets/v3/spreadsheets"
        
    def create_sheet(self, title: str = None, folder_token: str = None) -> dict:
        payload = {
            "title": title,
            "folder_token": folder_token
        }
        res = self.request("POST", self.createApi, payload)
        return res.get("data")
      
    def update_dimension_range(self, spreadsheet_token: str, sheet_id: str, major_dimension: str, startIndex: int, endIndex: int, size: int):
        api = f"{self.api}/{spreadsheet_token}/dimension_range"
        payload = {
            "dimension": {
                "sheetId": sheet_id,
                "majorDimension": major_dimension,
                "startIndex": startIndex,
                "endIndex": endIndex
            },
            "dimensionProperties": {
                "fixedSize": size
            }
        }
        res = self.request("PUT", api, payload)
        return res.get("data")
      


class FileClient(Client):
    def __init__(self, app_id: str, app_secret: str, timeout:int = 2000) -> Client:
        super().__init__(app_id, app_secret, timeout)
        self.api = f"/open-apis/drive/v1/files"

    def get_files(self, page_size = None, page_token = None, folder_token = None, order_by = None, direction = None, user_id_type = None):
        api = self.api
        # 添加参数到api上
        api = self.api + "?" + "&".join([
            f"page_size={page_size}" if page_size else "",
            f"page_token={page_token}" if page_token else "",
            f"folder_token={folder_token}" if folder_token else "",
            f"order_by={order_by}" if order_by else "",
            f"direction={direction}" if direction else "",
            f"user_id_type={user_id_type}" if user_id_type else "",
        ])
        res = self.request("GET", api)
        return res.get("data")
        pass 
      
    def get_files_old(self, folder_token = None, types = None):
        api = f"/open-apis/drive/explorer/v2/folder/{folder_token}/children"
        payload = {}
        if types:
            payload["types"] = types
        res = self.request("GET", api, payload)

        # 将 children 字典转换为列表
        if res.get("code") == 0 and res.get("data") and res.get("data").get("children"):
            children_dict = res["data"]["children"]
            children_list = []
            for token, file_info in children_dict.items():
                children_list.append(file_info)

            # 更新返回结果
            res["data"]["children"] = children_list

        return res.get("data")
        
    def get_folder_metadata(self, folder_token):
        api = f"/open-apis/drive/explorer/v2/folder/{folder_token}/metadata"
        res = self.request("GET", api)
        return res.get("data")
        pass
      
    def create_folder(self, name, parent_folder_token):
        api = f"/open-apis/drive/v1/files/create_folder"
        payload = {
            "name": name,
            "folder_token": parent_folder_token
        }
        res = self.request("POST", api, payload)
        return res.get("data")
        pass
      
    def delete_file(self, file_token, file_type):
        api = f"/open-apis/drive/v1/files/{file_token}=type{file_type}"
        res = self.request("DELETE", api)
        return res.get("data")

class DocClient(Client):
    def __init__(self, app_id: str, app_secret: str, timeout: int = 2000) -> Client:
        super().__init__(app_id, app_secret, timeout)
        self.api = "/open-apis/docx/v1/documents"

    def create_document(self, title: str = None, folder_token: str = None) -> dict:
        """创建文档"""
        payload = {
            "title": title,
            "folder_token": folder_token
        }
        res = self.request("POST", self.api, payload)
        return res.get("data")

    def get_document(self, document_id: str, user_id_type: str = "open_id") -> dict:
        """获取文档信息"""
        api = f"{self.api}/{document_id}"
        params = f"?user_id_type={user_id_type}"
        res = self.request("GET", api + params)
        return res.get("data")

    def get_document_content(self, document_id: str, user_id_type: str = "open_id", lang: int = 0) -> dict:
        """获取文档所有块的富文本内容"""
        api = f"{self.api}/{document_id}/blocks"
        params = f"?user_id_type={user_id_type}&lang={lang}"
        res = self.request("GET", api + params)
        return res.get("data")

    def get_block_children(self, document_id: str, block_id: str, user_id_type: str = "open_id", page_size: int = 500, page_token: str = None) -> dict:
        """获取指定块的所有子块"""
        api = f"{self.api}/{document_id}/blocks/{block_id}/children"
        params = [f"user_id_type={user_id_type}", f"page_size={page_size}"]
        if page_token:
            params.append(f"page_token={page_token}")
        res = self.request("GET", api + "?" + "&".join(params))
        return res.get("data")

    def create_block(self, document_id: str, block_id: str, children: list, index: int = -1, user_id_type: str = "open_id") -> dict:
        """在指定位置插入块"""
        api = f"{self.api}/{document_id}/blocks/{block_id}/children"
        payload = {
            "children": children,
            "index": index
        }
        xbot.logging.info(f"payload: {payload}")
        params = f"?user_id_type={user_id_type}"
        res = self.request("POST", api + params, payload)
        return res.get("data")
      
    def create_descendant_block(self, document_id: str, block_id: str, children_id: list, descendant: list, index: int = -1, user_id_type: str = "open_id") -> dict:
        """在指定位置插入块"""
        api = f"{self.api}/{document_id}/blocks/{block_id}/descendant"
        payload = {
            "children_id": children_id,
            "descendants": descendant,
            "index": index
        }
        params = f"?user_id_type={user_id_type}"
        res = self.request("POST", api + params, payload)
        return res.get("data")

    def update_block(self, document_id: str, block_id: str, elements: list, user_id_type: str = "open_id") -> dict:
        """更新块内容"""
        api = f"{self.api}/{document_id}/blocks/{block_id}"
        payload = {
            "elements": elements
        }
        params = f"?user_id_type={user_id_type}"
        res = self.request("PATCH", api + params, payload)
        return res.get("data")

    def delete_block(self, document_id: str, block_id: str, user_id_type: str = "open_id") -> dict:
        """删除块"""
        api = f"{self.api}/{document_id}/blocks/{block_id}"
        params = f"?user_id_type={user_id_type}"
        res = self.request("DELETE", api + params)
        return res.get("data")

def main(args):
    pass
