import os
import pandas as pd
from typing import Optional, Dict, Any
from .lark import insert_heading3, insert_ordered_list, insert_sheet, insert_text, get_doc_client
from .lark_sheet import get_instance, udpate_column_size
from .utils import filter_csv_by_bu_name, dataframe_to_sheet_data, update_sheet_data, extract_top_n, dataframe_to_sheet_width
from .logger import logger

# 时效异常配置常量
TIME_EFFICIENCY_CONFIG = [
    {
        "title": "【排期T-2 TC无收货】",
        "file": "大盘数据监控明细-TC未收货",
    },
    {
        "title": "【TC在T-4之前收货无发货】",
        "file": "大盘数据监控明细-TC无发货"
    },
    {
        "title": "【TC7天前发货但没有目的仓签到时间】",
        "file": "大盘监控明细-TC发货"
    },
    {
        "title": "【T-2直送无签到时间】",
        "file": "大盘监控明细-直送"
    }
]

TIME_EFFICIENCY_REPORT_URL = "https://youdata.yx.netease.com/dash/folder/3352?rid=35391"

# 数据处理配置
DATA_PROCESSING_CONFIG = {
    "arrival": {
        "title": "采购到货异常情况（含到货异常）",
        "file_pattern": "到货异常单量-{date}.csv",
        "bu_column": "商品BU名称",
        "summary_template": "到货异常：截止{date} {bu_name} 共{count}单到货异常，请及时跟进处理",
        "sum_column": "待处理单量",
        "report_url": "https://youdata.yx.netease.com/dash/folder/3352?rid=35339"
    },
    "approval": {
        "title": "锁库审批未完成情况",
        "file_pattern": "待审批锁定库存申请工单明细-{date}.csv",
        "bu_column": "商品BU名称",
        "summary_template": "锁库审批：截止{date} {bu_name} 共{count}单锁库未完成审批，请及时跟进处理",
        "max_rows": 3,
        "report_url": "https://youdata.yx.netease.com/dash/folder/3352?rid=35341"
    },
    "yuncang": {
        "title": "云仓低库转清单",
        "file_pattern": "云仓低库转监控-{date}.csv",
        "bu_column": "商品BU名称",
        "summary_template": "云仓低库转：截止{date} {bu_name} 共{count}单云仓低库转，请及时跟进处理",
        "max_rows": 3,
        "report_url": "https://youdata.yx.netease.com/dash/folder/3352?rid=35274&did=92808"
    }
}


def _process_csv_data(csv_path: str, bu_column: str, bu_name: str) -> Optional[pd.DataFrame]:
    """
    处理CSV数据的公共方法

    Args:
        csv_path: CSV文件路径
        bu_column: BU列名
        bu_name: BU名称

    Returns:
        筛选后的DataFrame，如果文件不存在或处理失败返回None
    """
    if not os.path.exists(csv_path):
        logger.warning(f"文件不存在: {csv_path}")
        return None

    try:
        data = filter_csv_by_bu_name(csv_path, bu_column, bu_name)
        logger.info(f"成功处理CSV文件: {csv_path}, 数据行数: {len(data)}")
        return data
    except Exception as e:
        logger.error(f"处理CSV文件失败 {csv_path}: {str(e)}")
        return None


def _insert_data_section(doc_client, document_id: str, parent_block_id: str,
                        data: Optional[pd.DataFrame], config: Dict[str, Any],
                        bu_name: str, date: str) -> None:
    """
    插入数据段的公共方法

    Args:
        doc_client: 文档客户端
        document_id: 文档ID
        parent_block_id: 父块ID
        data: 数据DataFrame
        config: 配置信息
        bu_name: BU名称
        date: 日期
    """
    try:
        if data is None:
            insert_text(doc_client, document_id, parent_block_id, "数据文件不存在")
            return

        if data.empty:
            insert_text(doc_client, document_id, parent_block_id, "暂无数据")
            logger.info("数据为空，插入暂无数据提示")
            return

        # 如果配置中有汇总模板，插入汇总信息
        if "summary_template" in config:
            # 如果config中有sum_column，则使用sum函数计算对应列的数据和，否则使用count函数计算行数
            if "sum_column" in config:
                count = data[config["sum_column"]].sum()
            else:
                count = len(data)
            summary = config["summary_template"].format(
                date=date, bu_name=bu_name, count=count
            )
            block_result = insert_ordered_list(doc_client, document_id, parent_block_id, summary)
            target_block_id = block_result['children'][0]['block_id'] if 'children' in block_result else parent_block_id
        else:
            target_block_id = parent_block_id

        # 插入电子表格
        sheet_result = insert_sheet(doc_client, document_id, target_block_id)
        sheet_token = sheet_result["sheet_token"]
        sheet_id = sheet_result["sheet_id"]

        # 写入数据
        sheet_data = dataframe_to_sheet_data(data)
        max_rows = config.get("max_rows", len(sheet_data))
        sheet_data = sheet_data[:max_rows]

        if sheet_data:
            sheet_instance = get_instance(sheet_token, sheet_id)
            update_sheet_data(sheet_instance, sheet_data, sheet_id)
            logger.info("数据写入成功")
            # 更新列宽
            sheet_widths = dataframe_to_sheet_width(data)
            for i, width in enumerate(sheet_widths, 1):
                udpate_column_size(sheet_token, sheet_id, i, i, width)

    except Exception as e:
        logger.error(f"插入数据段失败: {str(e)}")
        insert_text(doc_client, document_id, parent_block_id, f"数据处理失败: {str(e)}")


def _insert_data_section_with_title(doc_client, document_id: str, parent_block_id: str,
                                   config: Dict[str, Any], bu_name: str, date: str) -> str:
    """
    插入带标题的数据段的公共方法（包含列表项创建和数据处理）

    Args:
        doc_client: 文档客户端
        document_id: 文档ID
        parent_block_id: 父块ID
        config: 配置信息
        bu_name: BU名称
        date: 日期

    Returns:
        str: 创建的列表项block_id
    """
    try:
        # 插入有序列表项，设置文本样式为加粗
        list_result = insert_ordered_list(doc_client, document_id, parent_block_id,
                                        config["title"], style={"bold": True})
        list_block_id = list_result['children'][0]['block_id']
        logger.info(f"{config['title']}列表项插入成功，block_id: {list_block_id}")

        # 处理数据
        csv_path = f"C:\\KPI监控\\{date}\\{config['file_pattern'].format(date=date)}"
        data = _process_csv_data(csv_path, config["bu_column"], bu_name)
        _insert_data_section(doc_client, document_id, list_block_id, data,
                           config, bu_name, date)
        
        insert_ordered_list(doc_client, document_id, list_block_id, "详细数据可点击查看", link_url=config["report_url"])
        logger.info(f"详细信息链接插入成功: {config['report_url']}")

        return list_block_id

    except Exception as e:
        logger.error(f"插入带标题的数据段失败: {str(e)}")
        raise e


def _process_time_efficiency_item(doc_client, document_id: str, parent_block_id: str,
                                 item_config: Dict[str, str], bu_name: str, date: str) -> None:
    """
    处理单个时效异常项的公共方法

    Args:
        doc_client: 文档客户端
        document_id: 文档ID
        parent_block_id: 父块ID
        item_config: 时效异常项配置
        bu_name: BU名称
        date: 日期
    """
    try:
        title_text = item_config["title"]
        file_name = item_config["file"]

        # 插入子列表项
        item_result = insert_ordered_list(doc_client, document_id, parent_block_id, title_text)
        item_block_id = item_result['children'][0]['block_id']

        # 构建文件路径并处理数据
        csv_path = f"C:\\KPI监控\\{date}\\{file_name}-{date}.csv"
        # data = _process_csv_data(csv_path, "商品BU名称", bu_name)
        data = extract_top_n(csv_path, "商品BU名称", output_csv_path=None, top_n=3)

        if data is None:
            insert_text(doc_client, document_id, item_block_id, "数据文件不存在")
        elif data.empty:
            insert_text(doc_client, document_id, item_block_id, "暂无数据")
            logger.info(f"{title_text} 数据为空，插入暂无数据提示")
        else:
            # 插入电子表格并写入数据
            sheet_result = insert_sheet(doc_client, document_id, item_block_id)
            sheet_token = sheet_result["sheet_token"]
            sheet_id = sheet_result["sheet_id"]

            sheet_data = dataframe_to_sheet_data(data)
            if sheet_data:
                sheet_instance = get_instance(sheet_token, sheet_id)
                update_sheet_data(sheet_instance, sheet_data, sheet_id)
                logger.info(f"{title_text} 数据写入成功")
                # 更新列宽
                sheet_widths = dataframe_to_sheet_width(data)
                for i, width in enumerate(sheet_widths, 1):
                    udpate_column_size(sheet_token, sheet_id, i, i, width)

    except Exception as e:
        logger.error(f"处理时效异常项 {item_config.get('title', '未知')} 失败: {str(e)}")


def create_biz_abnormal_content(bu_name: str, date: str, document_id: str, root_block_id: str = None) -> str:
    """
    创建业务运作异常到飞书文档

    Args:
        bu_name: BU名称（字符串）
        date: 目标日期（格式：YYYY-MM-DD）
        document_id: 飞书文档ID
        root_block_id: 根块ID，如果为None则自动获取

    Returns:
        str: 创建的文档block_id或相关标识符
    """
    try:
        # 参数验证
        if not bu_name or not isinstance(bu_name, str):
            raise ValueError("bu_name 必须是非空字符串")
        
        if not date or not isinstance(date, str):
            raise ValueError("date 必须是非空字符串")

        if not document_id or not isinstance(document_id, str):
            raise ValueError("document_id 必须是非空字符串")
        
        logger.info(f"开始创建业务运作异常内容，BU名称: {bu_name}, 日期: {date}")
        
        # 创建DocClient实例
        docClient = get_doc_client()

        # 获取文档根块ID（如果未提供）
        if root_block_id is None:
            doc_info = docClient.get_document_content(document_id)
            root_block_id = doc_info["items"][0]["block_id"]
            logger.info(f"自动获取根块ID: {root_block_id}")

        logger.info("开始创建采购异常部分")
        insert_heading3(docClient, document_id, root_block_id, "三、业务运作异常")
        logger.info("三级标题插入成功")

        # 采购到货异常情况
        arrival_config = DATA_PROCESSING_CONFIG["arrival"]
        purchase_block_id = _insert_data_section_with_title(docClient, document_id, root_block_id,
                                                          arrival_config, bu_name, date)
        
        logger.info("开始创建时效异常部分")
        
        # 采购到货异常-时效异常
        efficiency_result = insert_ordered_list(docClient, document_id, purchase_block_id, "时效异常")
        efficiency_block_id = efficiency_result['children'][0]['block_id']
        logger.info(f"时效异常列表项插入成功，block_id: {efficiency_block_id}")
        
        # 时效异常数据
        for item in TIME_EFFICIENCY_CONFIG:
            _process_time_efficiency_item(docClient, document_id, efficiency_block_id,
                                        item, bu_name, date)
            
        insert_ordered_list(docClient, document_id, efficiency_block_id, "详细数据可点击查看", link_url=TIME_EFFICIENCY_REPORT_URL)
        logger.info(f"详细信息链接插入成功: {TIME_EFFICIENCY_REPORT_URL}")
        
        # 锁库审批未完成情况
        logger.info("开始锁库审批未完成部分")
        approval_config = DATA_PROCESSING_CONFIG["approval"]
        _insert_data_section_with_title(docClient, document_id, root_block_id,
                                       approval_config, bu_name, date)
        
        # 云仓低库转清单
        logger.info("开始云仓低库转清单部分")
        yuncang_config = DATA_PROCESSING_CONFIG["yuncang"]
        _insert_data_section_with_title(docClient, document_id, root_block_id,
                                       yuncang_config, bu_name, date)
        
        logger.info(f"业务运作异常内容创建完成，BU名称: {bu_name}")
        return root_block_id
        
    except Exception as e:
        logger.error(f"创建业务运作异常内容失败: {str(e)}")
        raise e


def main():
    """
    测试函数
    """
    try:
        # 测试参数
        test_bu_name = "宠物工作室"
        test_date = "2025-07-22"
        test_document_id = "P2lJdbin9oyyY8xoqa2c6bL2nXg"  # 示例文档ID

        # 调用函数
        result = create_biz_abnormal_content(test_bu_name, test_date, test_document_id)
        print(f"创建成功，返回block_id: {result}")

    except Exception as e:
        print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    main()
