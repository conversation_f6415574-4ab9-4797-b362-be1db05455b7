import xbot_visual
from requests_toolbelt import MultipartEncoder
from .client import <PERSON><PERSON><PERSON>
from .logger import logger
from .lark_sheet import get_file_list

# 获取docClient
def get_doc_client():
    _, user_name, password = xbot_visual.asset.get_asset(
        asset_name="飞书机器人",
        asset_type="certificate",
        encrypt_flag="1",
        asset_info="{\"asset_id\":\"2dc6461a-6956-4872-8b04-ac1f36bedaf8\",\"asset_template\":null}",
        _block=("main", 4, "获取资产")
    )
    real_password = xbot_visual.decrypt(password)
    docClient = DocClient(app_id=user_name, app_secret=real_password)
    return docClient

# 创建飞书文档
def create_doc(title, folder_token):
    docClient = get_doc_client()

    res = docClient.create_document(title, folder_token)
    
    return res['document']['document_id']

# 获取文档根节点
def get_root_block_id(docClient, document_id):
    doc_info = docClient.get_document_content(document_id)
    root_block_id = doc_info["items"][0]["block_id"]
    return root_block_id

# 创建块
def create_block(docClient, document_id, block_id, children, index=-1):
    res = docClient.create_block(document_id, block_id, children, index)
    return res

# 插入文本到文档
def insert_text(docClient, document_id, block_id, text, text_type="text", index=-1, link_url=None, style=None):
    """
    插入文本到文档
    :param docClient: 文档客户端实例
    :param document_id: 文档ID
    :param block_id: 块ID
    :param text: 文本内容
    :param text_type: 文本类型，支持：text, heading1-heading9, bullet_list, ordered_list
    :param link_url: 可选的超链接地址
    :param index: 插入位置
    :return: 响应结果
    """
    # 文本类型映射
    type_mapping = {
        "text": 2,           # 文本块
        "heading1": 3,       # 一级标题
        "heading2": 4,       # 二级标题
        "heading3": 5,       # 三级标题
        "heading4": 6,       # 四级标题
        "heading5": 7,       # 五级标题
        "heading6": 8,       # 六级标题
        "heading7": 9,       # 七级标题
        "heading8": 10,      # 八级标题
        "heading9": 11,      # 九级标题
        "bullet": 12,   # 无序列表
        "ordered": 13   # 有序列表
    }
    
    block_type = type_mapping.get(text_type, 2)
    
    # 构建文本运行元素
    text_run = {"content": text}
    
    # 如果提供了链接URL，添加链接样式
    if link_url:
        text_run["text_element_style"] = {
            "link": {
                "url": link_url
            }
        }

    if style:
        text_run["text_element_style"] = {
          **text_run.get("text_element_style", {}),
          **style
        }
    
    children = [{
        "block_type": block_type,
        text_type: {
            "elements": [{
                "text_run": text_run
            }]
        }
    }]
    
    res = docClient.create_block(document_id, block_id, children, index)
    return res

# 插入一级标题
def insert_heading1(docClient, document_id, block_id, text, index=-1, link_url=None, style=None):
    return insert_text(docClient, document_id, block_id, text, "heading1", index, link_url=link_url, style=style)

# 插入二级标题
def insert_heading2(docClient, document_id, block_id, text, index=-1, link_url=None, style=None):
    return insert_text(docClient, document_id, block_id, text, "heading2", index, link_url=link_url, style=style)

# 插入三级标题
def insert_heading3(docClient, document_id, block_id, text, index=-1, link_url=None, style=None):
    return insert_text(docClient, document_id, block_id, text, "heading3", index, link_url=link_url, style=style)

# 插入无序列表
def insert_bullet_list(docClient, document_id, block_id, text, index=-1, link_url=None, style=None):
    return insert_text(docClient, document_id, block_id, text, "bullet", index, link_url=link_url, style=style)

# 插入有序列表
def insert_ordered_list(docClient, document_id, block_id, text, index=-1, link_url=None, style=None):
    return insert_text(docClient, document_id, block_id, text, "ordered", index, link_url=link_url, style=style)

# 批量插入多个文本块
def insert_multiple_texts(docClient, document_id, block_id, text_items, index=-1):
    """
    批量插入多个文本块
    :param text_items: 文本项列表，每项包含 {"text": "内容", "type": "类型"}
    """
    children = []
    type_mapping = {
        "text": 2, "heading1": 3, "heading2": 4, "heading3": 5, "heading4": 6,
        "heading5": 7, "heading6": 8, "heading7": 9, "heading8": 10, "heading9": 11,
        "bullet": 12, "ordered": 13
    }
    
    for item in text_items:
        block_type = type_mapping.get(item.get("type", "text"), 2)
        children.append({
            "block_type": block_type,
            item.get("type", "text"): {
                "elements": [{
                    "text_run": {
                        "content": item["text"]
                    }
                }]
            }
        })
    
    res = docClient.create_block(document_id, block_id, children, index)
    return res

# 插入图片到文档（按照飞书文档三步流程）
def insert_image(docClient, document_id, block_id, image_path, width=None, height=None, index=-1):
    """
    按照飞书文档的三步流程插入图片：
    1. 创建图片Block
    2. 上传图片素材
    3. 设置图片Block的素材

    Args:
        docClient: 文档客户端实例
        document_id: 文档ID
        block_id: 父块ID
        image_path: 图片文件路径
        width: 图片宽度（可选）
        height: 图片高度（可选）
        index: 插入位置

    Returns:
        dict: 响应结果
    """
    import os

    # 检查图片文件是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在: {image_path}")

    # 第一步：创建图片Block
    children = [{
        "block_type": 27,  # 图片块
        "image": {}
    }]

    create_result = docClient.create_block(document_id, block_id, children, index)
    image_block_id = create_result['children'][0]['block_id']

    # 第二步：上传图片素材
    file_name = os.path.basename(image_path)
    file_size = os.path.getsize(image_path)

    # 使用 MultipartEncoder 构建multipart表单数据
    form_data = {
        'file_name': file_name,
        'parent_type': 'docx_image',
        'parent_node': image_block_id,
        'size': str(file_size),
        'file': open(image_path, 'rb')
    }

    # 创建 MultipartEncoder 实例
    multi_form = MultipartEncoder(form_data)
    logger.info(f"图片上传数据: {multi_form.content_type}")

    # 上传图片素材
    api = "/open-apis/drive/v1/medias/upload_all"

    # 使用 MultipartEncoder 的数据和 content_type
    upload_result = docClient.request("POST", api, multi_form, content_type=multi_form.content_type)

    # 关闭文件
    # form_data['file'][1].close()
    file_token = upload_result["data"]["file_token"]

    # 第三步：设置图片Block的素材
    update_api = f"/open-apis/docx/v1/documents/{document_id}/blocks/{image_block_id}"

    # 构建更新数据
    update_data = {
        "replace_image": {
            "token": file_token
        }
    }

    # 如果指定了宽度和高度，添加到更新数据中
    if width:
        update_data["replace_image"]["width"] = width
    
    if height:
        update_data["replace_image"]["height"] = height

    update_result = docClient.request("PATCH", update_api, update_data)

    return {
        "image_block_id": image_block_id,
        "file_token": file_token,
        "create_result": create_result,
        "upload_result": upload_result,
        "update_result": update_result
    }

# 插入表格到文档
def insert_table(docClient, document_id, block_id, table_data, index=-1):
    children = [{
        "block_type": 31,  # 表格块
        "table": table_data
    }]
    
    res = docClient.create_block(document_id, block_id, children, index)
    return res

# 插入电子表格到文档
def insert_sheet(docClient, document_id, block_id, row_size=3, column_size=3, index=-1):
    children = [{
        "block_type": 30,  # 电子表格块
        "sheet": {
            "row_size": 9 if row_size > 9 else row_size,
            "column_size": 9 if column_size > 9 else column_size
        }
    }]
    
    res = docClient.create_block(document_id, block_id, children, index)
    
    token = res['children'][0]['sheet']['token']
    sheet_token, sheet_id = token.split('_')
    return {
      "sheet_token": sheet_token,
      "sheet_id": sheet_id
    }

# 插入附件到文档（按照飞书文档三步流程）
def insert_file(docClient, document_id, block_id, file_path, file_name=None, index=-1):
    """
    按照飞书文档的三步流程插入文件：
    1. 创建文件Block
    2. 上传文件素材
    3. 设置文件Block的素材

    Args:
        docClient: 文档客户端实例
        document_id: 文档ID
        block_id: 父块ID
        file_path: 文件路径
        file_name: 文件名（可选，默认使用文件路径中的文件名）
        index: 插入位置

    Returns:
        dict: 响应结果
    """
    import os

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    # 如果没有指定文件名，使用文件路径中的文件名
    if not file_name:
        file_name = os.path.basename(file_path)

    # 第一步：创建文件Block
    children = [{
        "block_type": 23,  # 附件块
        "file": {}
    }]

    create_result = docClient.create_block(document_id, block_id, children, index)
    file_block_id = create_result['children'][0]['children'][0]

    # 第二步：上传文件素材
    file_size = os.path.getsize(file_path)

    # 使用 MultipartEncoder 构建multipart表单数据
    form_data = {
        'file_name': file_name,
        'parent_type': 'docx_file',
        'parent_node': file_block_id,
        'size': str(file_size),
        'file': open(file_path, 'rb')
    }

    # 创建 MultipartEncoder 实例
    multi_form = MultipartEncoder(fields=form_data)

    # 上传文件素材
    api = "/open-apis/drive/v1/medias/upload_all"

    # 使用 MultipartEncoder 的数据和 content_type
    upload_result = docClient.request("POST", api, multi_form, content_type=multi_form.content_type)
    file_token = upload_result["data"]["file_token"]

    # 关闭文件
    # form_data['file'][1].close()

    # 第三步：设置文件Block的素材
    update_api = f"/open-apis/docx/v1/documents/{document_id}/blocks/{file_block_id}"

    # 构建更新数据
    update_data = {
        "replace_file": {
            "token": file_token
        }
    }

    update_result = docClient.request("PATCH", update_api, update_data)

    return {
        "file_block_id": file_block_id,
        "file_token": file_token,
        "file_name": file_name,
        "create_result": create_result,
        "upload_result": upload_result,
        "update_result": update_result
    }

# 插入高亮块到文档
def insert_callout(docClient, document_id, block_id, content, color=1, index=-1):
    """
    插入高亮块到文档
    :param docClient: 文档客户端实例
    :param document_id: 文档ID
    :param block_id: 块ID
    :param content: 高亮块内容
    :param color: 颜色
    :param index: 插入位置
    :return: 响应结果
    """
    
    descendant = [{
        "block_id": "callout_id_1",
        "block_type": 19,  # 高亮块
        "callout": {
            "background_color": color,
            "border_color": color
        },
        "children": ["text_id_1"]
    }, {
        "block_id": "text_id_1",
        "block_type": 2,
        "text": {
            "elements": [{
                "text_run": {
                    "content": content
                }
            }]
        }
    }]
    
    children_id = ["callout_id_1"]
    
    res = docClient.create_descendant_block(document_id, block_id, children_id, descendant, index)
    return res
  
# 查询飞书目录是否已经存在文件
def is_exist_doc_file(folder_token, title):
    """
    查询指定文件夹下是否存在指定标题的文件
    """
    file_list_res = get_file_list(folder_token)
    # 按编辑时间倒序
    for file in file_list_res["files"]:
        if file["name"] == title and file["type"] == "docx":
            return file
    return None

def main(args):    
      # 创建DocClient实例
      document_id="P2lJdbin9oyyY8xoqa2c6bL2nXg"
      image_path = "C:\\KPI监控\\2025-07-22\\temp_chart_销量计划达成率_宠物工作室_2025-07-22.png"
      docClient = get_doc_client()
          # 获取文档根块ID
      doc_info = docClient.get_document_content(document_id)
      root_block_id = doc_info["items"][0]["block_id"]
      insert_image(docClient, document_id, root_block_id, image_path, width=None, height=None, index=-1)
