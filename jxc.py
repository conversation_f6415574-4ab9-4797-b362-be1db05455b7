# 使用此指令前，请确保安装必要的Python库，例如使用以下命令安装：
# pip install pandas numpy openpyxl

import pandas as pd
import numpy as np
from datetime import timedelta
from .utils import calculate_period_comparison, parse_date_column, format_date_to_MMDD
from .logger import logger
from .package import variables as glv

def parse_numeric_value(value):
    """
    解析不同格式的数值，支持百分比、小数、千分位字符串

    Args:
        value: 输入值，可能是字符串、数字等

    Returns:
        float: 解析后的数值，解析失败返回 np.nan
    """
    if pd.isna(value) or value is None:
        return np.nan

    if isinstance(value, (int, float)):
        return float(value)

    if isinstance(value, str):
        # 移除空格
        value = value.strip()

        # 空字符串处理
        if not value:
            return np.nan

        # 处理百分比
        if value.endswith('%'):
            try:
                return float(value[:-1]) / 100
            except ValueError:
                return np.nan

        # 处理千分位分隔符
        value = value.replace(',', '')

        try:
            return float(value)
        except ValueError:
            return np.nan

    return np.nan


def clean_dataframe_numeric_columns(df, numeric_columns):
    """
    清理DataFrame中的数值列，将各种格式转换为标准数值

    Args:
        df: pandas DataFrame
        numeric_columns: 需要清理的数值列名列表

    Returns:
        pandas DataFrame: 清理后的DataFrame
    """
    df_cleaned = df.copy()

    for col in numeric_columns:
        if col in df_cleaned.columns:
            df_cleaned[col] = df_cleaned[col].apply(parse_numeric_value)

    return df_cleaned


def process_data(data_type, input_file, output_file):
    """
    title: 进销存-数据整理与分析
    description: 读取数据CSV文件，按照要求整理数据并计算环比变化和同比变化，输出结果到新的CSV文件。
    inputs:
        - data_type: 数据类型，可选值：销量销额、成本、库存水位、采购入库、ToC出库数量、ToC出库单量、ToB出库数量
        - input_file (file): 输入的CSV文件路径
        - output_file (str): 输出的CSV文件保存路径
    outputs:
        - output_file (str): 处理后的CSV文件路径
    """
    # 读取输入CSV文件
    df = pd.read_csv(input_file)

    # 确保数据类型正确
    df['日期'] = pd.to_datetime(df['日期'])

    # 注意：源数据已经是千分位、百分比等格式，不需要重新处理
    # 保持原始数据格式，仅在计算同环比时进行数值转换
    
    wan_format_list = [
        '【SKU+渠道】销量（含赠品）',
        '【商品销售额】商品销售金额（计算折扣率用，不含运费,含渠道补贴）',
        '京东自营销额',
        '总销售库存成本',
        '当日销售成本(全站含赠品)',
        '当日采购入库成本',
        '良品在库库存'
    ]
    
    # 定义需要处理的指标列及其映射
    metrics_mapping = {
        '销量销额': {
          '【SKU+渠道】销量（含赠品）': '全渠道销量（含京东）',
          '【商品销售额】商品销售金额（计算折扣率用，不含运费,含渠道补贴）': '全渠道销额（含京东）',
          '京东自营销额': '京东自营销额',
        },
        '成本': {
          '总销售库存成本': '自营库存成本',
          '当日销售成本(全站含赠品)': '销售成本（含京东）',
          '当日采购入库成本': '采购入库成本',
        },
        '库存水位': {
          '良品在库库存': '实物库存水位',
          '良品在库库存体积': '实物库存体积',
          '云仓库存占比': '云仓库存占比',
          '云仓库存体积': '云仓库存体积',
          '商品件均体积': '商品件均体积',
        },
        '采购入库': {
          '采购入库量': '采购入库件数',
          '云仓采购入库占比': '云仓采购入库占比',
          '仓间调拨入库量': '调拨入库件数',
          '云仓调拨入库占比': '云仓调拨入库占比',
        },
        'ToC出库数量': {
          '实际出库数量（含锁定）': '销售出库件数',
          '云仓出库占比': '云仓出库占比',
        },
        'ToC出库单量': {
          '出库单数量': '销售出库单量',
          '云仓单量占比': '云仓单量占比',
        },
        'ToB出库数量': {
          '非销售出库（含京东）': '非销售出库（含京东）',
          '调拨出库件数': '调拨出库件数',
        }
    }
    
    # 获取目标日期（从全局变量中获取）
    target_date = parse_date_column(glv['target_date'])
    logger.info(f"目标日期: {target_date.strftime('%Y-%m-%d')}")

    # 计算前8天的日期范围（从target_date开始往前推8天）
    date_range = []
    for i in range(7, -1, -1):  # 7, 6, 5, 4, 3, 2, 1, 0 (对应T-8到T-1)
        date_val = target_date - timedelta(days=i)
        date_range.append(date_val)

    logger.info(f"日期范围: {[d.strftime('%Y-%m-%d') for d in date_range]}")

    # 创建结果DataFrame
    result_data = []

    # 按BU名称和数据分类分组处理数据
    for (bu, category), group in df.groupby(['商品BU名称', '数据分类']):
        # 对每个指标进行处理
        for old_metric, new_metric in metrics_mapping[data_type].items():
            # 准备前8天的数据（保存原始显示值和转换后的数值）
            date_data_display = {}  # 原始显示值（保持千分位、百分比格式）
            date_data_numeric = {}  # 转换后的数值（用于计算同环比）

            # 遍历前8天的日期
            for date_val in date_range:
                # 查找该日期的数据
                day_data = group[group['日期'].dt.date == date_val.date()]

                if not day_data.empty:
                    # 获取原始显示值（保持原格式）
                    display_value = day_data[old_metric].values[0]
                    # 转换为数值类型用于计算
                    numeric_value = parse_numeric_value(display_value)

                    # 如果old_metric在wan_format_list里，就转换成万为单位，保留两位小数
                    if old_metric in wan_format_list:
                        display_value = f"{numeric_value/10000:.2f}万"

                    # 使用format_date_to_MMDD格式化日期作为列名
                    formatted_date = format_date_to_MMDD(date_val.strftime('%Y-%m-%d'))
                    date_data_display[formatted_date] = display_value
                    date_data_numeric[formatted_date] = numeric_value
                else:
                    # 没有数据时填充NaN
                    formatted_date = format_date_to_MMDD(date_val.strftime('%Y-%m-%d'))
                    date_data_display[formatted_date] = np.nan
                    date_data_numeric[formatted_date] = np.nan

            # 计算同环比变化（使用最新的3个日期点）
            sorted_dates = sorted(date_data_numeric.keys(), key=lambda x: parse_date_column(f"2024-{x}"))

            if len(sorted_dates) >= 3:
                # 获取最新、次新、最早的数值用于计算同环比
                latest_value = date_data_numeric[sorted_dates[-1]]  # 最新日期
                second_latest_value = date_data_numeric[sorted_dates[-2]]  # 次新日期
                earliest_value = date_data_numeric[sorted_dates[0]]  # 最早日期

                chain_ratio, year_on_year = calculate_period_comparison(
                    latest_value, second_latest_value, earliest_value, old_metric
                )
            else:
                chain_ratio, year_on_year = 'N/A', 'N/A'

            # 构建行数据
            row_data = {
                '商品BU名称': bu,
                '数据分类': category,
                '数据指标': new_metric,  # 使用映射后的指标名称
                '环比变化': chain_ratio,
                '同比变化': year_on_year
            }

            # 添加各日期的数据（按时间顺序）
            for date_val in date_range:
                formatted_date = format_date_to_MMDD(date_val.strftime('%Y-%m-%d'))
                row_data[formatted_date] = date_data_display.get(formatted_date, np.nan)

            result_data.append(row_data)
    
    # 创建结果DataFrame并保存
    result_df = pd.DataFrame(result_data)
    
    # 优化输出格式
    result_df = result_df.sort_values(['商品BU名称', '数据分类'])
    
    # 将结果保存到CSV文件
    result_df.to_csv(output_file, index=False)
    
    return output_file

def modify_csv_headers(input_file, output_file):
    """
    title: 修改CSV文件表头
    description: 将输入CSV文件的A1:A3单元格内容修改为日期、商品BU名称、数据分类，并保存为新文件。
    inputs: 
        - input_file (file): 输入的CSV文件路径，eg: "原始数据.csv"
        - output_file (str): 修改后保存的文件路径，eg: "修改后数据.csv"
    outputs: 
        - output_file (str): 修改后的CSV文件路径，eg: "修改后数据.csv"
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 获取列名列表
    columns = df.columns.tolist()
    
    # 确保至少有3列
    if len(columns) < 3:
        raise ValueError("CSV文件至少需要3列才能修改A1:A3的内容")
    
    # 修改前3列的列名
    new_columns = columns.copy()
    new_columns[0] = "日期"
    new_columns[1] = "商品BU名称"
    new_columns[2] = "数据分类"
    
    # 更新DataFrame的列名
    df.columns = new_columns
    
    # 保存修改后的CSV文件
    df.to_csv(output_file, index=False)
    
    return output_file


def combine_csv_files(input_files, output_file):
    """
    title: 合并多个CSV文件
    description: 将多个CSV文件合并成一个文件
    inputs: 
        - input_files (list): 输入的CSV文件路径列表
        - output_file (str): 输出的CSV文件保存路径
    outputs: 
        - output_file (str): 合并后的CSV文件路径
    """
    # 读取所有输入CSV文件
    dfs = [pd.read_csv(file) for file in input_files]
    
    # 检查列名是否一致
    column_names = dfs[0].columns
    for df in dfs[1:]:
        if not df.columns.equals(column_names):
            raise ValueError("所有CSV文件的列名必须一致")
    
    # 合并所有DataFrame
    combined_df = pd.concat(dfs, ignore_index=True)
    
    # 将合并后的数据保存到输出文件
    combined_df.to_csv(output_file, index=False)
    
    return output_file
  
def process_jsx_data(folder, date):
    # folder = f"C:\KPI监控\{date}"
    input_files = [
        f"{folder}\进销存数据看板-销量销额-{date}.csv",
        f"{folder}\进销存数据看板-成本-{date}.csv",
        f"{folder}\进销存数据看板-库存水位-{date}.csv",
        f"{folder}\进销存数据看板-采购入库-{date}.csv",
        f"{folder}\进销存数据看板-ToC出库数量-{date}.csv",
        f"{folder}\进销存数据看板-ToC出库单量-{date}.csv",
        f"{folder}\进销存数据看板-ToB出库数量-{date}.csv"
    ]
    output_files = [
        f"{folder}\processed\进销存数据看板-销量销额-{date}.csv",
        f"{folder}\processed\进销存数据看板-成本-{date}.csv",
        f"{folder}\processed\进销存数据看板-库存水位-{date}.csv",
        f"{folder}\processed\进销存数据看板-采购入库-{date}.csv",
        f"{folder}\processed\进销存数据看板-ToC出库数量-{date}.csv",
        f"{folder}\processed\进销存数据看板-ToC出库单量-{date}.csv",
        f"{folder}\processed\进销存数据看板-ToB出库数量-{date}.csv"
    ]
    # 调用modify_csv_headers函数修改input_files中的CSV文件表头
    for input_file in input_files:
        modify_csv_headers(input_file, input_file)
    
    # 调用process_data函数处理数据
    for input_file, output_file in zip(input_files, output_files):
        # 从input_file中提取文件名
        file_name = input_file.split("\\")[-1]
        data_type = file_name.split("-")[1]
        process_data(data_type, input_file, output_file)
    
    # 合并所有处理后的文件
    combine_csv_files(output_files, f"{folder}\processed\进销存数据看板-汇总-{date}.csv")

def main(args=None):
    """
    主函数，处理今日的进销存数据

    Args:
        args: 命令行参数（可选）
    """
    from datetime import date
    today = date.today().strftime("%Y-%m-%d")
    process_jsx_data('C:\\KPI监控\\2025-07-29', '2025-07-29')
