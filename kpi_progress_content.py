import os
import base64
from typing import Optional, Dict, Any, Tuple
from .lark import insert_ordered_list, insert_sheet, insert_image, insert_text, get_doc_client
from .chart import generate_chart
from .utils import filter_csv_by_bu_name
from .logger import logger
from .utils import convert_to_numeric, calculate_period_comparison, insert_sheet_with_data
from .package import variables as glv


def _validate_parameters(bu_name: str, date: str, title: str, config: dict, youdata_link: str) -> None:
    """
    验证输入参数的公共方法

    Args:
        bu_name: BU名称
        date: 日期
        title: 标题
        config: 配置字典
        youdata_link: 有数链接

    Raises:
        ValueError: 参数验证失败时抛出
    """
    if not bu_name or not isinstance(bu_name, str):
        raise ValueError("bu_name参数不能为空且必须是字符串")
    if not date or not isinstance(date, str):
        raise ValueError("date参数不能为空且必须是字符串")
    if not title or not isinstance(title, str):
        raise ValueError("title参数不能为空且必须是字符串")
    if not config or not isinstance(config, dict):
        raise ValueError("config参数不能为空且必须是字典")
    if not youdata_link or not isinstance(youdata_link, str):
        raise ValueError("youdata_link参数不能为空且必须是字符串")

    # 验证配置参数结构
    if "line_chart" not in config:
        raise ValueError("config中缺少line_chart配置")
    if "detail_table" not in config:
        raise ValueError("config中缺少detail_table配置")

    line_chart_config = config["line_chart"]
    required_chart_fields = ["name", "date_column"]
    for field in required_chart_fields:
        if field not in line_chart_config:
            raise ValueError(f"line_chart配置中缺少{field}字段")

    # 检查顶级配置中的必需字段
    required_top_fields = ["kpi_column", "kpi_name"]
    for field in required_top_fields:
        if field not in config:
            raise ValueError(f"config中缺少{field}字段")


def _build_file_path(date: str, filename: str) -> str:
    """
    构建文件路径的公共方法

    Args:
        date: 日期
        filename: 文件名（不含扩展名）

    Returns:
        完整的文件路径
    """
    return os.path.join("C:\\KPI监控", date, "processed", f"{filename}-{date}.csv")


def _load_and_filter_data(file_path: str, bu_column: str, bu_name: str, data_type: str = "数据"):
    """
    加载和筛选数据的公共方法

    Args:
        file_path: 文件路径
        bu_column: BU列名
        bu_name: BU名称
        data_type: 数据类型描述（用于错误信息）

    Returns:
        筛选后的DataFrame

    Raises:
        FileNotFoundError: 文件不存在时抛出
        ValueError: 数据为空时抛出
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"{data_type}文件不存在: {file_path}")

    data = filter_csv_by_bu_name(file_path, bu_column, bu_name)

    if data.empty:
        raise ValueError(f"未找到BU名称为 '{bu_name}' 的{data_type}")

    logger.info(f"成功加载{data_type}: {file_path}, 数据行数: {len(data)}")
    return data


def _calculate_kpi_metrics(data, kpi_column: str) -> Tuple[Any, Any, float, float]:
    """
    计算KPI指标的公共方法

    Args:
        data: 数据DataFrame
        kpi_column: KPI列名

    Returns:
        (t1_value, t2_value, chain_ratio, year_on_year)

    Raises:
        ValueError: 计算失败时抛出
    """
    if kpi_column not in data.columns:
        raise ValueError(f"KPI列 '{kpi_column}' 在数据中不存在")

    if len(data) < 2:
        raise ValueError("数据记录不足，无法计算环比变化")

    # 获取最新和次新的指标数据
    t1_value = data.iloc[-1][kpi_column]  # T-1数据
    t2_value = data.iloc[-2][kpi_column]  # T-2数据
    t8_value = data.iloc[-8][kpi_column]  # T-8数据

    # 转换为数值类型进行计算
    try:
        t1_numeric = convert_to_numeric(t1_value)
        t2_numeric = convert_to_numeric(t2_value)
        t8_numeric = convert_to_numeric(t8_value)
    except Exception as e:
        raise ValueError(f"KPI数据转换失败: {str(e)}")
      
    chain_ratio, year_on_year = calculate_period_comparison(
        t1_numeric, t2_numeric, t8_numeric, kpi_column
    )

    return t1_value, t2_value, chain_ratio, year_on_year


def create_kpi_progress_content(bu_name: str, date: str, title: str, config: dict, youdata_link: str, document_id: str = None) -> str:
    """
    创建KPI进度内容到飞书文档

    Args:
        bu_name: BU名称（字符串）
        date: 目标日期（格式：YYYY-MM-DD）
        title: 区块标题（字符串）
        config: 配置字典，包含图表和表格配置信息
        youdata_link: 有数报表链接（完整URL）
        document_id: 飞书文档ID（可选，如果不提供则创建新文档）

    Returns:
        str: 创建的文档block_id或相关标识符
    """
    # 参数验证
    _validate_parameters(bu_name, date, title, config, youdata_link)

    logger.info(f"开始创建KPI进度内容，BU名称: {bu_name}, 日期: {date}, 标题: {title}")

    # 创建DocClient实例
    docClient = get_doc_client()

    # 步骤1：创建区块标题
    if not document_id:
        raise ValueError("document_id参数不能为空，需要指定目标文档ID")

    # 获取文档根块ID
    doc_info = docClient.get_document_content(document_id)
    root_block_id = doc_info["items"][0]["block_id"]

    # 插入有序列表项作为区块标题，内容为title，设置文本样式为加粗
    title_result = insert_ordered_list(docClient, document_id, root_block_id, title, style={"bold": True})
    title_block_id = title_result['children'][0]['block_id']
    logger.info(f"区块标题插入成功: {title}, block_id: {title_block_id}")

    # 步骤2：生成和插入折线图
    line_chart_config = config["line_chart"]
    line_chart_path = _build_file_path(date, line_chart_config['name'])

    # 加载和筛选折线图数据
    line_chart_data = _load_and_filter_data(line_chart_path, line_chart_config["bu_column"], bu_name, "折线图数据")

    # 按第一列（时间列）排序
    time_column = line_chart_data.columns[0]
    # line_chart_data = line_chart_data.sort_values(time_column)

    # 计算KPI指标
    t1_value, t2_value, chain_ratio, year_on_year = _calculate_kpi_metrics(
        line_chart_data, line_chart_config["columns"][-1]
    )

    # 生成折线图
    chart_data = []
    kpi_column = line_chart_config["columns"][-1]
    # 使用第一列作为时间列
    for _, row in line_chart_data.iterrows():
        chart_data.append({
            "time": str(row[time_column]),
            "value": row[kpi_column]
        })

    chart_config = {
        # "title": f"{bu_name} {config['kpi_name']}趋势",
        "width": 1200,
        "height": 300,
        "x_field": "time",
        "y_field": "value",
        "x_axis_title": line_chart_config["date_column"],
        "y_axis_title": config["kpi_name"]
    }

    # 生成图表并保存为临时文件
    base64_image = generate_chart(chart_data, "line", chart_config)
    temp_image_path = os.path.join("C:\\KPI监控", date, f"temp_chart_{config['kpi_name']}_{bu_name}_{date}.png")

    # 确保目录存在
    os.makedirs(os.path.dirname(temp_image_path), exist_ok=True)

    # 保存图片
    with open(temp_image_path, "wb") as f:
        f.write(base64.b64decode(base64_image))

    logger.info(f"折线图生成成功: {temp_image_path}")

    # 在title_block_id对应的区块内插入有序列表项，内容格式：{date} {bu_name} {kpi_name} {T-1指标值}，环比变化 {环比变化}
    kpi_text = f"{glv['target_date']} {bu_name} {config['kpi_name']} {t1_value}，环比变化 {chain_ratio}"
    kpi_result = insert_ordered_list(docClient, document_id, title_block_id, kpi_text)
    kpi_block_id = kpi_result['children'][0]['block_id']
    logger.info(f"KPI指标文本插入成功: {kpi_text}")

    # 上传并插入图片到当前有序列表块内
    insert_image(docClient, document_id, kpi_block_id, temp_image_path, 1200, 300)
    logger.info("折线图插入到文档成功")

    # 步骤3：处理明细表格数据
    detail_table_path = _build_file_path(date, config['detail_table']['name'])
    detail_data = _load_and_filter_data(detail_table_path, "商品BU名称", bu_name, "明细表格数据")

    # 插入明细表格
    insert_sheet_with_data(docClient, document_id, kpi_block_id, detail_data, "明细表格")

    # 步骤4：处理TOP表格数据（如果存在）
    top_data = None
    if config.get("top_table"):
        top_table_path = _build_file_path(date, config['top_table']['name'])

        if os.path.exists(top_table_path):
            # 在title_block_id对应的区块内插入有序列表项
            top_text = "TOP异常商品清单如下，请关注并跟进解决"
            top_result = insert_ordered_list(docClient, document_id, title_block_id, top_text)
            top_block_id = top_result['children'][0]['block_id']
            logger.info(f"TOP异常提示文本插入成功: {top_text}")

            try:
                top_data = _load_and_filter_data(top_table_path, "商品BU名称", bu_name, "TOP表格数据")
                insert_sheet_with_data(docClient, document_id, top_block_id, top_data, "TOP表格")
            except ValueError as e:
                logger.warning(f"'{bu_name}' 的TOP表格数据为空，插入暂无数据: {str(e)}")
                insert_text(docClient, document_id, top_block_id, "暂无数据")
                top_data = None
        else:
            logger.warning(f"TOP表格数据文件不存在: {top_table_path}")

    # 步骤5：插入详细信息链接
    # 在title_block_id对应的区块内插入有序列表项，内容为超链接
    insert_ordered_list(docClient, document_id, title_block_id, "详细数据可点击查看", link_url=youdata_link)
    logger.info(f"详细信息链接插入成功: {youdata_link}")

    # 清理临时文件
    try:
        if os.path.exists(temp_image_path):
            os.remove(temp_image_path)
            logger.info(f"临时图片文件已删除: {temp_image_path}")
    except Exception as e:
        logger.warning(f"删除临时文件失败: {str(e)}")

    logger.info(f"KPI进度内容处理完成: {title} {bu_name}")

    # 返回创建的标题块ID
    return title_block_id


def main():
    """主函数，用于测试"""
    # 测试配置
    test_config =  {
        "title": "【供应】销量计划达成率",
        "kpi_column": "计划达成率",
        "kpi_name": "销量计划达成率",
        "report_url": "https://youdata.yx.netease.com/dash/folder/3352?rid=34495&did=89133",
        "line_chart": {
            "name": "BU 销量计划达成率趋势-周度",
            "columns": ["分区日期", "商品BU名称", "计划达成率"],
            "date_column": "分区日期",
            "bu_column": "商品BU名称"
        },
        "detail_table": {
            "name": "BU 销量计划达成率明细-周度",
            "date_column": "分区日期",
            "bu_column": "商品BU名称",
            "add_total_column": True
        },
        "top_table": {
            "name": "周度TOP异常-SKU",
            "bu_column": "商品BU名称",
            "top_n": 5
        }
    }
    
    try:
        result = create_kpi_progress_content(
            bu_name="宠物工作室",
            date="2025-07-22",
            title="【供应】销量计划达成率",
            config=test_config,
            youdata_link="https://youdata.yx.netease.com/dash/folder/3352?rid=34495&did=89133",
            document_id="P2lJdbin9oyyY8xoqa2c6bL2nXg"  # 实际使用时需要提供真实的文档ID
        )
        logger.info(f"测试完成，返回结果: {result}")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
