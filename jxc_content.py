import os
import pandas as pd
from .lark import insert_heading3, insert_ordered_list, insert_sheet, get_doc_client
from .lark_sheet import get_instance, update_data_by_range, set_cell_format, merge_cells, udpate_column_size
from .logger import logger
from .utils import get_excel_column_name, dataframe_to_sheet_data, filter_csv_by_bu_name, get_merge_range, dataframe_to_sheet_width
from .package import variables as glv

def create_jxc_content(document_id: str, input_csv_path: str, bu_name: str) -> str:
    """
    创建进销存内容的飞书文档。

    Args:
        folder (str): 文件夹路径（字符串类型）- 可以是：
                     1. CSV文件的完整路径
                     2. 包含CSV文件的文件夹路径
                     3. 飞书文件夹token（需要在代码中指定CSV文件路径）
        bu_name (str): BU名称（字符串类型）

    Returns:
        str: 创建的文档ID

    Raises:
        Exception: 当创建文档或插入内容失败时抛出异常
    """
    logger.info(f"开始创建进销存内容文档，BU名称: {bu_name}")
        
    # 提取指定BU的数据（假设BU列名为"商品BU名称"，根据实际情况调整）
    bu_column = "商品BU名称"
    filtered_data = filter_csv_by_bu_name(input_csv_path, bu_column, bu_name)
    # 计算数据分类列可以合并单元格的返回，返回一个列表，如["B2:B4", "B5:B7"]
    merge_range = get_merge_range(filtered_data, '数据分类')
    
    if filtered_data.empty:
        raise ValueError(f"未找到BU名称为 '{bu_name}' 的数据")
    
    # 创建DocClient实例
    docClient = get_doc_client()
    
    # 获取文档根块ID
    doc_info = docClient.get_document_content(document_id)
    root_block_id = doc_info["items"][0]["block_id"]
    
    # 3. 按顺序插入内容
    
    # a. 插入H3级别标题文本："一、进销存情况"
    insert_heading3(docClient, document_id, root_block_id, "一、进销存情况")
    logger.info("H3标题插入成功")
    
    # b. 插入有序列表项："请关注以下商品BU维度进销存数据，数据来源于有数报表"
    list_result = insert_ordered_list(docClient, document_id, root_block_id, "请关注以下商品BU维度进销存数据，数据来源于有数报表")
    logger.info("有序列表项插入成功")
    
    # 获取刚插入的有序列表块的ID
    list_block_id = list_result['children'][0]['block_id']
    
    # c. 在当前有序列表块内嵌入电子表格
    # 根据数据确定表格大小
    row_size = len(filtered_data) + 1  # 数据行数 + 表头行
    column_size = len(filtered_data.columns)  # 列数
    
    sheet_result = insert_sheet(docClient, document_id, list_block_id, row_size, column_size)
    sheet_token = sheet_result["sheet_token"]
    logger.info(f"电子表格插入成功，sheet_token: {sheet_token}")
    
    # 将DataFrame数据写入电子表格
    sheet_data = dataframe_to_sheet_data(filtered_data)
    sheet_widths = dataframe_to_sheet_width(filtered_data)
    if sheet_data:
        # 获取表格实例
        sheet_instance = get_instance(sheet_token)
        
        # 计算数据范围（A1到最后一个单元格）
        end_column = get_excel_column_name(column_size)  # 计算最后一列的字母
        data_range = f"A1:{end_column}{row_size}"
        
        # 写入数据到表格
        update_data_by_range(sheet_instance, data_range, sheet_data)
        # 标题加粗
        set_cell_format(sheet_instance, f"A1:{end_column}1", {"font_bold": True, "formatter": "@"})
        set_cell_format(sheet_instance, f"A2:C{row_size}", {"font_bold": True})
        # 设置成纯文本
        set_cell_format(sheet_instance, f"D2:{end_column}{row_size}", {"formatter": "@"})
        # 合并第一列
        merge_cells(sheet_instance, f"A2:A{row_size}")

        # 合并数据分类列的相同值单元格
        if merge_range:
            for range_str in merge_range:
                try:
                    merge_cells(sheet_instance, range_str)
                    logger.info(f"合并单元格成功: {range_str}")
                except Exception as e:
                    logger.warning(f"合并单元格失败 {range_str}: {str(e)}")

        logger.info("数据写入电子表格成功")
        
        # 更新列宽
        for i, width in enumerate(sheet_widths, 1):
            udpate_column_size(sheet_token, sheet_result['sheet_id'], i, i, width)
    
    # d. 插入新的有序列表项，内容为超链接
    link_url = "https://youdata.yx.netease.com/dash/folder/3352?rid=35274&ignoreDefaultBookmark=true"
    insert_ordered_list(docClient, document_id, root_block_id, "详细数据可点击查看", link_url=link_url)
    logger.info("超链接有序列表项插入成功")
    
    logger.info(f"进销存内容文档创建完成，文档ID: {document_id}")
    return document_id

def main(args):
    """主函数"""
    create_jxc_content("P2lJdbin9oyyY8xoqa2c6bL2nXg", "C:\\KPI监控\\2025-07-22\\processed\\进销存数据看板-汇总-2025-07-22.csv", "宠物工作室")
    pass
