import pandas as pd
import os
import re
import numpy as np
from typing import List, Union, Tuple
from .logger import logger
from .lark_sheet import update_data_by_range, set_cell_format, get_instance
from .lark import insert_sheet

# 明细表格
def reorganize_csv_data(
    input_csv_path: str,
    date_column: str,
    bu_column: str,
    output_csv_path: str,
    add_total_column: bool = True
) -> Union[pd.DataFrame, None]:
    """
    将CSV数据从列式布局转换为行式布局，以度量指标为行，日期为列的格式。

    Args:
        input_csv_path (str): 输入CSV文件的完整路径
        date_column (str): 日期列的列名（例如："支付日期"）
        bu_column (str): 业务单元列的列名（例如："商品BU名称"）
        output_csv_path (str): 输出CSV文件的保存路径
        add_total_column (bool): 是否需要添加总计列（True/False）

    Returns:
        pd.DataFrame or None: 处理后的DataFrame对象，如果处理失败则返回None
    """

    # 验证输入文件是否存在
    if not os.path.exists(input_csv_path):
        raise FileNotFoundError(f"输入文件不存在: {input_csv_path}")

    # 读取CSV文件
    logger.info(f"正在读取文件: {input_csv_path}")
    df = pd.read_csv(input_csv_path, encoding='utf-8')

    # 验证指定的列名是否存在
    required_columns = [date_column, bu_column]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"以下列名在CSV文件中不存在: {missing_columns}")

    original_metrics = [col for col in df.columns if col not in required_columns]

    logger.info(f"原始数据形状: {df.shape}")
    logger.info(f"原始列名: {list(df.columns)}")
    logger.info(f"度量指标: {original_metrics}")

    # 保留原数据格式，不进行数据类型转换
    df_cleaned = df.copy()

    # 创建结果列表
    result_rows = []

    # 按BU分组处理
    for bu_name in df_cleaned[bu_column].unique():
        bu_data = df_cleaned[df_cleaned[bu_column] == bu_name]

        # 为每个度量指标创建一行
        for original_metric in original_metrics:
            # 创建透视表：日期为列，度量指标值为数据
            pivot_data = bu_data.pivot_table(
                index=bu_column,
                columns=date_column,
                values=original_metric,
                aggfunc='sum',  # 如果同一日期有多条记录，则求和
                fill_value=0
            )

            # 构建结果行
            row_data = {
                '商品BU名称': bu_name,
                '度量指标': original_metric
            }

            # 添加总计列（如果需要）
            if add_total_column:
                # 为了计算总计，需要先转换数据类型
                numeric_series = pivot_data.iloc[0].apply(convert_to_numeric)
                total_value = _calculate_total(numeric_series, original_metric)

                # 根据原数据格式来格式化总计值
                formatted_total = _format_total_value(total_value, original_metric, pivot_data.iloc[0])
                row_data['总计'] = formatted_total

            # 添加各日期的数据
            for date_col in pivot_data.columns:
                row_data[date_col] = pivot_data.iloc[0][date_col]

            result_rows.append(row_data)

    # 创建结果DataFrame
    result_df = pd.DataFrame(result_rows)

    # 对日期列进行排序，确保日期从小到大排列
    if len(result_df) > 0:
        # 获取所有列名
        all_columns = list(result_df.columns)

        # 分离固定列和日期列
        fixed_columns = ['商品BU名称', '度量指标']
        if add_total_column:
            fixed_columns.append('总计')

        # 获取日期列（除了固定列之外的所有列）
        date_columns = [col for col in all_columns if col not in fixed_columns]

        try:
            # 使用自定义解析函数对日期列进行排序
            date_columns_with_parsed = [(col, parse_date_column(col)) for col in date_columns]
            # 过滤掉无法解析的日期列，并按解析后的日期排序
            valid_date_columns = [(col, parsed_date) for col, parsed_date in date_columns_with_parsed if pd.notna(parsed_date)]
            # 按解析后的日期排序
            valid_date_columns.sort(key=lambda x: x[1])

            # 创建日期列重命名映射，将日期格式化为MM/DD
            column_rename_mapping = {}
            date_columns_sorted = []

            for col, parsed_date in valid_date_columns:
                try:
                    # 使用MM/DD格式格式化日期列标题
                    formatted_date = parsed_date.strftime('%m/%d')
                    column_rename_mapping[col] = formatted_date
                    date_columns_sorted.append(formatted_date)
                    logger.info(f"日期列 '{col}' 格式化为 '{formatted_date}'")
                except Exception as e:
                    logger.warning(f"日期列 '{col}' 格式化失败，保持原名: {e}")
                    date_columns_sorted.append(col)

            # 如果有无法解析的日期列，添加到末尾（保持原名）
            invalid_date_columns = [col for col, parsed_date in date_columns_with_parsed if pd.isna(parsed_date)]
            date_columns_sorted.extend(invalid_date_columns)

            # 重命名日期列
            if column_rename_mapping:
                result_df = result_df.rename(columns=column_rename_mapping)
                logger.info(f"重命名日期列: {column_rename_mapping}")

        except Exception as e:
            logger.warning(f"日期列排序失败，使用原始顺序: {e}")
            date_columns_sorted = date_columns

        # 重新排列列的顺序：固定列 + 排序后的日期列
        new_column_order = fixed_columns + date_columns_sorted

        # 确保所有列都存在于DataFrame中
        existing_columns = [col for col in new_column_order if col in result_df.columns]
        result_df = result_df[existing_columns]

    # 确保输出目录存在
    output_dir = os.path.dirname(output_csv_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存结果
    result_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
    logger.info(f"数据重组完成，结果已保存到: {output_csv_path}")
    logger.info(f"输出数据形状: {result_df.shape}")

    return result_df


def convert_to_numeric(value) -> float:
    """
    将字符串值转换为数值，处理千分位分隔符和百分比格式。

    Args:
        value: 待转换的值

    Returns:
        float: 转换后的数值
    """
    if pd.isna(value):
        return 0.0

    if isinstance(value, (int, float)):
        return float(value)

    # 转换为字符串并去除空格
    str_value = str(value).strip()

    # 处理百分比格式
    if str_value.endswith('%'):
        try:
            # 移除百分号并转换为小数
            numeric_part = str_value[:-1].replace(',', '')
            return float(numeric_part) / 100.0
        except ValueError:
            return 0.0

    # 处理千分位分隔符
    try:
        # 移除千分位逗号
        cleaned_value = str_value.replace(',', '')
        return float(cleaned_value)
    except ValueError:
        # 如果转换失败，尝试提取数字
        numbers = re.findall(r'-?\d+\.?\d*', cleaned_value)
        if numbers:
            return float(numbers[0])
        return 0.0


def _calculate_total(series: pd.Series, metric_name: str) -> float:
    """
    计算总计值，根据度量指标类型决定计算方式。

    Args:
        series (pd.Series): 包含各日期数据的序列
        metric_name (str): 度量指标名称

    Returns:
        float: 计算后的总计值
    """
    # 对于率类指标，计算平均值；对于数量类指标，计算总和
    if any(keyword in metric_name for keyword in ['率', '比', '%', 'rate', 'ratio']):
        # 率类指标计算平均值
        return series.mean()
    else:
        # 数量类指标计算总和
        return series.sum()


def _format_total_value(total_value: float, metric_name: str, original_series: pd.Series) -> str:
    """
    根据原数据格式来格式化总计值。

    Args:
        total_value (float): 计算得到的总计值
        metric_name (str): 度量指标名称
        original_series (pd.Series): 原始数据序列，用于判断格式

    Returns:
        str: 格式化后的总计值
    """
    # 检查原数据是否包含百分比格式
    has_percentage = False
    has_comma = False

    for value in original_series:
        if pd.notna(value):
            str_value = str(value).strip()
            if str_value.endswith('%'):
                has_percentage = True
            if ',' in str_value and not str_value.endswith('%'):
                has_comma = True

    # 根据原数据格式来格式化总计值
    if has_percentage:
        # 百分比格式：将小数转换为百分比
        percentage_value = total_value * 100
        return f"{percentage_value:.2f}%"
    elif has_comma:
        # 千分位格式
        return f"{total_value:,.0f}" if total_value == int(total_value) else f"{total_value:,.2f}"
    else:
        # 普通数字格式
        return f"{total_value:.0f}" if total_value == int(total_value) else f"{total_value:.2f}"

# TOP商品
def extract_top_n(
    input_csv_path: str,
    bu_column: str,
    output_csv_path: str,
    top_n: int = None
) -> Union[pd.DataFrame, None]:
    """
    按业务单元聚合CSV数据，为每个BU保留指定数量的记录。

    Args:
        input_csv_path (str): 输入CSV文件的完整路径
        bu_column (str): 商品BU列的列名（例如："商品BU名称"）
        output_csv_path (str): 输出CSV文件的保存路径
        top_n (int, optional): 每个BU保留的记录数量。如果为None，则返回每个BU的所有数据（默认值：None）

    Returns:
        pd.DataFrame or None: 处理后的DataFrame对象，如果处理失败则返回None
    """
    # 验证输入文件是否存在
    if not os.path.exists(input_csv_path):
        raise FileNotFoundError(f"输入文件不存在: {input_csv_path}")

    # 读取CSV文件
    logger.info(f"正在读取文件: {input_csv_path}")
    df = pd.read_csv(input_csv_path, encoding='utf-8')

    # 验证指定的列名是否存在
    required_columns = [bu_column]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"以下列名在CSV文件中不存在: {missing_columns}")

    logger.info(f"原始数据形状: {df.shape}")
    logger.info(f"原始列名: {list(df.columns)}")
    logger.info(f"BU列 '{bu_column}' 的唯一值数量: {df[bu_column].nunique()}")

    # 处理数据筛选逻辑
    if top_n is None:
        # 如果top_n为None，返回所有数据（按BU分组但不限制数量）
        logger.info(f"正在按 '{bu_column}' 分组，返回每个BU的所有数据...")
        filtered_df = df.copy()
    else:
        # 验证top_n参数
        if top_n <= 0:
            raise ValueError(f"top_n参数必须大于0，当前值: {top_n}")

        # 按BU分组并为每个组保留前N条记录
        logger.info(f"正在按 '{bu_column}' 分组，每组保留前 {top_n} 条记录...")
        filtered_df = df.groupby(bu_column).head(top_n).reset_index(drop=True)

    logger.info(f"处理后数据形状: {filtered_df.shape}")

    # 显示每个BU的记录数量统计
    bu_counts = filtered_df[bu_column].value_counts().sort_index()
    if top_n is None:
        logger.info(f"各BU的记录数量（全部数据）:")
    else:
        logger.info(f"各BU筛选后的记录数量:")
    for bu_name, count in bu_counts.items():
        logger.info(f"  {bu_name}: {count} 条记录")

    # 统一将bu_column列名改为"商品BU名称"
    if bu_column != "商品BU名称":
        filtered_df = filtered_df.rename(columns={bu_column: "商品BU名称"})
        logger.info(f"已将列名 '{bu_column}' 重命名为 '商品BU名称'")

    # 确保输出目录存在
    output_dir = os.path.dirname(output_csv_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存结果
    filtered_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
    if top_n is None:
        logger.info(f"数据处理完成，结果已保存到: {output_csv_path}")
    else:
        logger.info(f"数据筛选完成，结果已保存到: {output_csv_path}")

    return filtered_df

# 折线图
def extract_csv_columns(
    input_csv_path: str,
    output_csv_path: str,
    columns: Union[List[str], dict]
) -> Union[pd.DataFrame, None]:
    """
    从CSV文件中提取指定列并生成新的CSV文件，支持列名重命名功能。

    Args:
        input_csv_path (str): 输入CSV文件的完整路径
        output_csv_path (str): 输出CSV文件的保存路径
        columns (list or dict): 列映射，支持两种格式：
            - 如果是列表：['支付日期', '商品BU名称', '缺货SKU数量'] (保持原列名)
            - 如果是字典：{'支付日期': '日期', '商品BU名称': 'BU名称', '有货率': None}
              字典格式支持重命名，值为None表示保持原列名

    Returns:
        pd.DataFrame or None: 处理后的DataFrame对象，如果处理失败则返回None
    """
    # 验证输入文件是否存在
    if not os.path.exists(input_csv_path):
        raise FileNotFoundError(f"输入文件不存在: {input_csv_path}")

    # 处理columns参数，支持列表和字典两种格式
    if isinstance(columns, dict):
        # 字典格式：{原列名: 新列名}
        if len(columns) == 0:
            raise ValueError("columns字典不能为空")
        original_columns = list(columns.keys())
        mapping_dict = columns
    elif isinstance(columns, list):
        # 列表格式：保持原列名
        if len(columns) == 0:
            raise ValueError("columns列表不能为空")
        original_columns = columns
        mapping_dict = {col: None for col in columns}
    else:
        raise ValueError("columns参数必须是列表或字典格式")

    # 读取CSV文件
    logger.info(f"正在读取文件: {input_csv_path}")
    df = pd.read_csv(input_csv_path, encoding='utf-8')

    logger.info(f"原始数据形状: {df.shape}")
    logger.info(f"原始列名: {list(df.columns)}")
    logger.info(f"列映射: {mapping_dict}")

    # 验证指定的列名是否存在
    missing_columns = [col for col in original_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"以下列名在CSV文件中不存在: {missing_columns}")

    # 提取指定列
    logger.info(f"正在提取列: {original_columns}")
    extracted_df = df[original_columns].copy()

    # 重命名列
    rename_mapping = {}
    for original_col, new_col in mapping_dict.items():
        if new_col is not None:
            rename_mapping[original_col] = new_col

    if rename_mapping:
        logger.info(f"正在重命名列: {rename_mapping}")
        extracted_df = extracted_df.rename(columns=rename_mapping)

    logger.info(f"提取后数据形状: {extracted_df.shape}")
    logger.info(f"提取后列名: {list(extracted_df.columns)}")

    # 确保输出目录存在
    output_dir = os.path.dirname(output_csv_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存结果
    extracted_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
    logger.info(f"列提取完成，结果已保存到: {output_csv_path}")

    return extracted_df



def filter_csv_by_bu_name(
    input_csv_path: str,
    bu_column: str,
    bu_name: str,
    output_csv_path: str = None
) -> pd.DataFrame:
    """
    按指定BU名称筛选CSV数据，提取该BU的全部数据记录。
    
    Args:
        input_csv_path (str): 输入CSV文件的完整路径
        bu_column (str): 商品BU列的列名（例如："商品BU名称"）
        bu_name (str): 要筛选的BU名称
        output_csv_path (str, optional): 输出CSV文件的保存路径，如果为None则不保存文件
    
    Returns:
        pd.DataFrame: 筛选后的DataFrame对象
    """
    # 验证输入文件是否存在
    if not os.path.exists(input_csv_path):
        raise FileNotFoundError(f"输入文件不存在: {input_csv_path}")
    
    # 读取CSV文件
    logger.info(f"正在读取文件: {input_csv_path}")
    df = pd.read_csv(input_csv_path, encoding='utf-8')
    
    # 验证指定的列名是否存在
    if bu_column not in df.columns:
        raise ValueError(f"列名 '{bu_column}' 在CSV文件中不存在")
    
    logger.info(f"原始数据形状: {df.shape}")
    logger.info(f"BU列 '{bu_column}' 的唯一值: {df[bu_column].unique()}")
    
    # 按BU名称筛选数据
    filtered_df = df[df[bu_column] == bu_name].copy()
    
    if filtered_df.empty:
        logger.warning(f"未找到BU名称为 '{bu_name}' 的数据")
        return filtered_df
    
    logger.info(f"筛选后数据形状: {filtered_df.shape}")
    logger.info(f"BU '{bu_name}' 的记录数量: {len(filtered_df)}")

    # 统一将bu_column列名改为"商品BU名称"
    if bu_column != "商品BU名称":
        filtered_df = filtered_df.rename(columns={bu_column: "商品BU名称"})
        logger.info(f"已将列名 '{bu_column}' 重命名为 '商品BU名称'")

    # 如果指定了输出路径，则保存文件
    if output_csv_path:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_csv_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存结果
        filtered_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        logger.info(f"筛选结果已保存到: {output_csv_path}")

    return filtered_df
  
def get_excel_column_name(column_number: int) -> str:
    """
    将列数字转换为Excel列名（如1->A, 26->Z, 27->AA）。

    Args:
        column_number (int): 列数字（从1开始）

    Returns:
        str: Excel列名
    """
    column_name = ""
    while column_number > 0:
        column_number -= 1
        column_name = chr(ord('A') + column_number % 26) + column_name
        column_number //= 26
    return column_name
  
  
def dataframe_to_sheet_data(df: pd.DataFrame) -> list:
    """
    将pandas DataFrame转换为飞书表格可接受的数据格式。

    Args:
        df (pd.DataFrame): 要转换的DataFrame

    Returns:
        list: 飞书表格数据格式，包含表头和数据行
    """
    if df.empty:
        return []

    # 转换为列表格式，第一行是表头
    data = []

    # 添加表头
    headers = df.columns.tolist()
    data.append(headers)

    # 添加数据行
    for _, row in df.iterrows():
        # 将所有值转换为字符串，处理NaN值
        row_data = []
        for value in row:
            if pd.isna(value):
                row_data.append("")
            else:
                row_data.append(str(value))
        data.append(row_data)

    return data
  
def update_sheet_data(sheet_instance, sheet_data, sheet_id):
    """
    更新飞书表格数据并设置格式

    Args:
        sheet_instance: 表格实例
        sheet_data: 表格数据（包含表头和数据行）
        row_size: 行数
        column_size: 列数
        sheet_id: 表格ID
    """
    # 计算数据范围（A1到最后一个单元格）
    row_size = len(sheet_data)
    column_size = len(sheet_data[0])
    end_column = get_excel_column_name(column_size)
    data_range = f"A1:{end_column}{row_size}"
    logger.info(f"数据范围: {data_range}")

    # 写入数据到表格
    update_data_by_range(sheet_instance, data_range, sheet_data, sheet_id)

    # 首行加粗
    header_range = f"A1:{end_column}1"
    set_cell_format(sheet_instance, header_range, {"font_bold": True, "formatter": "@"}, sheet_id)

    # 其余单元格为纯文本格式
    if row_size > 1:
        data_range_without_header = f"A2:{end_column}{row_size}"
        set_cell_format(sheet_instance, data_range_without_header, {"formatter": "@"}, sheet_id)

    logger.info("数据写入电子表格成功")
    
def get_merge_range(df: pd.DataFrame, column_name: str) -> list:
    """
    计算指定列中相同值的单元格合并范围。

    Args:
        df (pd.DataFrame): 数据框
        column_name (str): 要分析的列名

    Returns:
        list: 合并范围列表，如["B2:B4", "B5:B7"]
    """
    if df.empty or column_name not in df.columns:
        return []

    merge_ranges = []

    # 找到指定列在DataFrame中的位置（从0开始）
    column_index = df.columns.get_loc(column_name)
    # 转换为Excel列名（从A开始）
    excel_column = get_excel_column_name(column_index + 1)

    # 分析连续相同值的范围
    current_value = None
    start_row = None

    # 遍历数据行（从第2行开始，因为第1行是表头）
    for i, value in enumerate(df[column_name], start=2):  # Excel行号从2开始（跳过表头）
        if pd.isna(value):
            value = ""  # 将NaN转换为空字符串
        else:
            value = str(value)  # 确保是字符串类型

        if current_value is None:
            # 第一个值
            current_value = value
            start_row = i
        elif current_value == value:
            # 相同值，继续
            continue
        else:
            # 值发生变化，记录前一个范围
            if start_row < i - 1:  # 只有当范围包含多行时才需要合并
                merge_ranges.append(f"{excel_column}{start_row}:{excel_column}{i-1}")

            # 开始新的范围
            current_value = value
            start_row = i

    # 处理最后一个范围
    if start_row is not None:
        end_row = len(df) + 1  # 最后一行的Excel行号（数据行数+1，因为第1行是表头）
        if start_row < end_row:  # 只有当范围包含多行时才需要合并
            merge_ranges.append(f"{excel_column}{start_row}:{excel_column}{end_row}")

    return merge_ranges


# 对日期列进行排序
def parse_date_column(date_str):
    """
    解析日期列名，支持多种格式：
    - "6-16" -> 当年的6月16日
    - "2024-06-16" -> 2024年6月16日
    - "06-16" -> 当年的6月16日
    """
    try:
        date_str = date_str.replace("/", "-")
        # 如果是 "M-D" 或 "MM-DD" 格式，添加当前年份
        if '-' in str(date_str) and len(str(date_str).split('-')) == 2:
            current_year = pd.Timestamp.now().year
            date_with_year = f"{current_year}-{date_str}"
            return pd.to_datetime(date_with_year, errors='coerce')
        else:
          return pd.to_datetime(date_str, errors='coerce')
    except:
        pass

    # 如果都失败了，返回NaT
    return pd.NaT
  
# 将T-X转换成指定的日期，以MM/DD格式输出
def convert_t_x_to_date(t_x_str, base_date=None):
    """
    将"T-X"格式的字符串转换为指定基期的日期，并以"MM/DD"格式输出。

    Args:
        t_x_str (str): "T-X"格式的字符串，例如"T-1"表示基期的前1天。
        base_date (str): 基期日期字符串，格式为"YYYY-MM-DD"。

    Returns:
        str: 转换后的日期字符串，格式为"MM/DD"。
    """
    # base_date为None，默认为当天，并格式化成YYYY-MM-DD格式
    if base_date is None:
        base_date = pd.Timestamp.now().strftime('%Y-%m-%d')
    # 提取X的值
    x = int(t_x_str.split('-')[1])
    # 将基期转换为datetime对象
    base_date = pd.to_datetime(base_date)
    # 计算目标日期
    target_date = base_date - pd.DateOffset(days=x)
    # 格式化为MM/DD格式，并输出纯文本
    formatted_date = target_date.strftime('%m/%d')
    return formatted_date
  
# 将日期转换成MM/DD格式输出
def format_date_to_MMDD(date):
    """
    将日期以"MM/DD"格式输出。

    Args:
        date (str): 日期字符串。

    Returns:
        str: 转换后的日期字符串，格式为"MM/DD"。
    """
    # date为None，默认为当天，并格式化成YYYY-MM-DD格式
    if date is None:
        date = pd.Timestamp.now().strftime('%Y-%m-%d')
    # 将基期转换为datetime对象
    base_date = parse_date_column(date)
    # 格式化为MM/DD格式，并输出纯文本
    formatted_date = base_date.strftime('%m/%d')
    return formatted_date


def calculate_period_comparison(t1_value: float, t2_value: float, t8_value: float,
                               metric_name: str) -> Tuple[str, str]:
    """
    计算同环比变化的通用函数

    Args:
        t1_value (float): T-1期的数值（已转换为数值类型）
        t2_value (float): T-2期的数值（已转换为数值类型）
        t8_value (float): T-8期的数值（已转换为数值类型）
        metric_name (str): 指标名称，用于判断是否为占比类指标

    Returns:
        Tuple[str, str]: (环比变化, 同比变化)
    """
    # 计算环比变化（T-1 vs T-2）
    if not pd.isna(t1_value) and not pd.isna(t2_value):
        # 根据指标类型格式化变化值
        if metric_name.endswith('占比') or metric_name.endswith('率'):
            # 占比类指标：显示百分点变化
            chain_ratio = f"{(t1_value - t2_value)*100:.2f}%"
        else:
            # 其他指标：显示变化率
            chain_ratio = f"{(t1_value / t2_value - 1):.2%}" if t2_value != 0 else "N/A"
    else:
        chain_ratio = "N/A"

    # 计算同比变化（T-1 vs T-8）
    if not pd.isna(t1_value) and not pd.isna(t8_value) and t8_value != 0:
        # 根据指标类型格式化变化值
        if metric_name.endswith('占比') or metric_name.endswith('率'):
            # 占比类指标：显示百分点变化
            year_on_year = f"{(t1_value - t8_value)*100:.2f}%"
        else:
            # 其他指标：显示变化率
            year_on_year = f"{(t1_value / t8_value - 1):.2%}" if t8_value != 0 else "N/A"
    else:
        year_on_year = "N/A"

    return chain_ratio, year_on_year
  
def insert_sheet_with_data(doc_client, document_id: str, parent_block_id: str, data, sheet_description: str = "表格") -> None:
    """
    插入表格并写入数据的公共方法

    Args:
        doc_client: 文档客户端
        document_id: 文档ID
        parent_block_id: 父块ID
        data: 数据DataFrame
        sheet_description: 表格描述（用于日志）
    """
    # 插入电子表格
    sheet_result = insert_sheet(doc_client, document_id, parent_block_id)
    sheet_token = sheet_result["sheet_token"]
    sheet_id = sheet_result["sheet_id"]
    logger.info(f"{sheet_description}插入成功，{sheet_token}_{sheet_id}")

    # 将DataFrame数据写入电子表格
    sheet_data = dataframe_to_sheet_data(data)
    if sheet_data:
        # 获取表格实例
        sheet_instance = get_instance(sheet_token, sheet_id)
        # 更新表格数据
        update_sheet_data(sheet_instance, sheet_data, sheet_id)
        logger.info(f"{sheet_description}数据写入成功")