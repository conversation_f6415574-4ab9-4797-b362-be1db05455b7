import os
import glob
from datetime import date
from .jxc import process_jsx_data
from .kpi_progress import process_kpi_data, extract_kpi_summary_data
from .logger import logger
from .jxc_content import create_jxc_content
from .kpi_progress_content import create_kpi_progress_content
from .biz_abnormal_content import create_biz_abnormal_content
from .useful_report import create_report_content
from .lark import create_doc, insert_callout, insert_heading3, get_doc_client, is_exist_doc_file, insert_ordered_list
from .lark_sheet import get_sheet_client
from .const import DOC_FOLDER_TOKEN, KPI_CONFIG_LIST
from .notify import notify
from .package import variables as glv
from .utils import filter_csv_by_bu_name, insert_sheet_with_data, get_expired_file_list
from .client import FileClient
import xbot_visual

def process_data(date):
    """
    执行KPI数据处理的主要逻辑

    包括以下步骤：
    1. 文件目录创建
    2. 判断文件是否齐全
    3. 处理进销存数据
    4. 处理指标达成情况
    """

    # 1. 文件目录创建
    base_folder = f"C:\\KPI监控\\{date}"
    processed_folder = f"{base_folder}\\processed"

    logger.info(f"开始处理KPI数据，日期: {date}")
    logger.info(f"基础目录: {base_folder}")
    logger.info(f"处理目录: {processed_folder}")

    # 判断目录是否存在，若不存在则新建
    if not os.path.exists(processed_folder):
        os.makedirs(processed_folder)
        logger.info(f"✓ 创建目录: {processed_folder}")
    else:
        logger.info(f"✓ 目录已存在: {processed_folder}")

    # 2. 判断文件是否齐全
    csv_pattern = os.path.join(base_folder, "*.csv")
    csv_files = glob.glob(csv_pattern)
    csv_count = len(csv_files)

    logger.info(f"检查CSV文件数量: {csv_count}")

    if csv_count != 39:
        logger.warning(f"⚠️  警告: CSV文件数量不正确，期望39个，实际{csv_count}个")
        logger.info("文件列表:")
        for i, file_path in enumerate(csv_files, 1):
            filename = os.path.basename(file_path)
            logger.info(f"  {i:2d}. {filename}")
        raise Exception("CSV文件数量不正确")
        
    else:
        logger.info(f"✓ CSV文件数量正确 ({csv_count}个)")

    # 3. 处理进销存数据
    logger.info("\n" + "="*50)
    logger.info("开始处理进销存数据...")
    logger.info("="*50)

    process_jsx_data(base_folder, date)
    logger.info("✓ 进销存数据处理完成")

    # 4. 处理指标达成情况
    logger.info("\n" + "="*50)
    logger.info("开始处理指标达成情况...")
    logger.info("="*50)

    process_kpi_data(base_folder, date)
    logger.info("✓ 指标达成情况处理完成")
    
    # 汇总指标
    extract_kpi_summary_data(base_folder, date, f"{processed_folder}\\指标达成汇总-{date}.csv")

    logger.info("\n" + "="*50)
    logger.info("🎉 所有数据处理完成!")
    logger.info("="*50)

def create_kpi_monitor_doc(bu_name: str, date: str) -> str:
    """
    创建完整的KPI监控文档

    Args:
        bu_name: BU名称（字符串）
        date: 目标日期（格式：YYYY-MM-DD）

    Returns:
        str: 创建的文档ID
    """
    try:
        # 参数验证
        if not bu_name or not isinstance(bu_name, str):
            raise ValueError("bu_name 必须是非空字符串")

        if not date or not isinstance(date, str):
            raise ValueError("date 必须是非空字符串")

        logger.info(f"开始创建KPI监控文档，BU名称: {bu_name}, 日期: {date}")

        # 1. 创建文档
        target_date = date[2:].replace("-", "/")
        document_title = f"【{target_date}简报】进销存及KPI指标监控（{bu_name}）"
        logger.info(f"创建文档，标题: {document_title}")
        
        exist = is_exist_doc_file(DOC_FOLDER_TOKEN, document_title)
        if exist:
            logger.info(f"文档已存在，ID: {exist['token']}")
            return exist['token']

        # 创建DocClient实例
        docClient = get_doc_client()
        sheetClient = get_sheet_client()
        glv['sheet_client'] = sheetClient
        document_id = create_doc(title=document_title, folder_token=DOC_FOLDER_TOKEN)
        logger.info(f"文档创建成功，文档ID: {document_id}")

        # 获取文档根块ID
        doc_info = docClient.get_document_content(document_id)
        root_block_id = doc_info["items"][0]["block_id"]
        logger.info(f"获取文档根块ID: {root_block_id}")

        # 2. 插入高亮块
        callout_content = "此简报，是通过RPA机器人在业务运作层面初次尝试，后续将不断地进行完善，如有疑问或者字段新增，可以联系刘畅；以下所有字段的数据，均取自目前计划侧使用的自助分析模型，数据来源一致。"
        insert_callout(docClient, document_id, root_block_id, callout_content, color=4)
        logger.info("高亮块插入成功")

        # 3. 插入进销存内容
        jxc_csv_path = f"C:\\KPI监控\\{date}\\processed\\进销存数据看板-汇总-{date}.csv"
        logger.info(f"开始插入进销存内容，数据文件: {jxc_csv_path}")

        create_jxc_content(document_id, jxc_csv_path, bu_name)
        logger.info("进销存内容插入成功")

        # 4. 插入指标达成情况
        logger.info("开始插入指标达成情况")
        insert_heading3(docClient, document_id, root_block_id, "二、指标达成情况")
        logger.info("指标达成情况标题插入成功")
        
        # 指标汇总
        res = insert_ordered_list(docClient, document_id, root_block_id, '指标达成汇总', style={"bold": True})
        summary_data = filter_csv_by_bu_name(f"C:\\KPI监控\\{date}\\processed\\指标达成汇总-{date}.csv", "BU", bu_name)
        insert_sheet_with_data(docClient, document_id, res['children'][0]['block_id'], summary_data, "指标达成汇总")

        # 循环处理KPI列表
        for index, kpi_config in enumerate(KPI_CONFIG_LIST, 1):
          logger.info(f"处理KPI项 {index}/{len(KPI_CONFIG_LIST)}: {kpi_config['title']}")

          create_kpi_progress_content(
              bu_name=bu_name,
              date=date,
              title=kpi_config["title"],
              config=kpi_config,
              youdata_link=kpi_config["report_url"],
              document_id=document_id
          )

          logger.info(f"KPI项 {index} 插入成功: {kpi_config['title']}")

        logger.info("指标达成情况插入完成")

        # 5. 插入业务运作异常
        logger.info("开始插入业务运作异常")
        create_biz_abnormal_content(bu_name, date, document_id, root_block_id)
        logger.info("业务运作异常插入成功")

        # 6. 插入常用有数报表
        logger.info("开始插入常用有数报表")
        create_report_content(document_id, bu_name, date)
        logger.info("常用有数报表插入成功")

        logger.info(f"KPI监控文档创建完成，文档ID: {document_id}")
        return document_id

    except Exception as e:
        logger.error(f"创建KPI监控文档失败: {str(e)}")
        notify(f"创建KPI监控文档失败: {bu_name} {str(e)}")
        raise e


def test_create_doc(test_date, test_bu_name):
    """
    测试创建文档功能
    """
    try:
        logger.info("开始测试创建文档功能")
        logger.info(f"测试参数 - BU名称: {test_bu_name}, 日期: {test_date}")

        # 调用创建文档函数
        document_id = create_kpi_monitor_doc(test_bu_name, test_date)

        logger.info(f"✅ 文档创建测试成功，文档ID: {document_id}")
        print(f"文档创建成功，文档ID: {document_id}")

        return document_id

    except Exception as e:
        logger.error(f"❌ 文档创建测试失败: {str(e)}")
        print(f"文档创建失败: {str(e)}")
        raise e


def delete_expired_file(delta_day=7) -> dict:
    """
    删除指定文件夹下过期的docx文件

    Args:
        delta_day (int): 天数差值，删除多少天以前创建的文件

    Returns:
        dict: 删除结果统计，包含成功数量、失败数量和详细信息

    Raises:
        Exception: 当删除过程中发生严重错误时抛出异常
    """
    try:
        folder_token = DOC_FOLDER_TOKEN
        logger.info(f"开始删除文件夹 {folder_token} 中 {delta_day} 天前创建的过期docx文件")

        # 使用get_expired_file_list获取过期文件token列表
        expired_files = get_expired_file_list(folder_token, delta_day)

        if not expired_files:
            logger.info("未找到需要删除的过期文件")

        logger.info(f"找到 {len(expired_files)} 个过期文件，开始删除...")

        # 创建FileClient实例
        _, user_name, password = xbot_visual.asset.get_asset(
            asset_name="飞书机器人",
            asset_type="certificate",
            encrypt_flag="1",
            asset_info="{\"asset_id\":\"2dc6461a-6956-4872-8b04-ac1f36bedaf8\",\"asset_template\":null}",
            _block=("main", 4, "获取资产")
        )
        real_password = xbot_visual.decrypt(password)
        file_client = FileClient(user_name, real_password)
        success_count = 0
        failed_count = 0

        # 逐个删除文件
        for i, file in enumerate(expired_files, 1):
            try:
                logger.info(f"正在删除文件 {i}/{len(expired_files)}: {file['name']} (token: {file['token']})")

                # 调用FileClient的delete_file方法删除文件
                result = file_client.delete_file(file['token'], file['type'])

                success_count += 1
                logger.info(f"✅ 文件删除成功: {file['name']}")

            except Exception as e:
                failed_count += 1
                error_msg = f"删除文件 {file['name']} 失败: {str(e)}"
                logger.error(f"❌ {error_msg}")

                # 继续删除其他文件，不中断整个过程
                continue

        # 汇总结果
        result_summary = {
            "total_count": len(expired_files),
            "success_count": success_count,
            "failed_count": failed_count
        }

        logger.info(f"文件删除完成！总计: {len(expired_files)}, "
                   f"成功: {success_count}, 失败: {failed_count}")

        if failed_count > 0:
            logger.warning(f"有 {failed_count} 个文件删除失败，请检查日志")

        return result_summary

    except Exception as e:
        logger.error(f"删除过期文件过程中发生严重错误: {str(e)}")
        raise e

def main(args=None):
    """
    主函数

    Args:
        args: 命令行参数（可选）
    """
    # 默认执行数据处理
    process_data('2025-07-31')

    # 取消注释以下行来测试文档创建功能
    test_create_doc('2025-07-31', '宠物工作室')

    # 取消注释以下行来测试删除过期文件功能
    # delete_expired_file()  # 删除7天前的文件
