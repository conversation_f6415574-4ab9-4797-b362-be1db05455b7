from .lark import insert_heading3, insert_ordered_list, get_doc_client, insert_file
from .logger import logger
from .utils import filter_csv_by_bu_name
import os
import zipfile

# 有数报表列表配置
YOUDATA_REPORTS = [
    {
        "name": "【基础】SKU基础信息表",
        "url": "https://youdata.yx.netease.com/dash/folder/3352?rid=33094",
        "file": "SKU基础信息表"
    },
    {
        "name": "【销售库存】采购销售库存跟踪",
        "url": "https://youdata.yx.netease.com/dash/folder/3352?rid=33200",
        "file": "SKU采购销售库转详情-日"
    },
    {
        "name": "【ToC销售】全渠道销售表现",
        "url": "https://youdata.yx.netease.com/dash/folder/3352?rid=33202&did=83690",
        "file": "全渠道销售表现-日"
    },
    {
        "name": "【进销存】实物进销存看板（T+1）",
        "url": "https://youdata.yx.netease.com/dash/folder/3352?rid=33115",
        "file": "SKU实物进销存明细表-仅内仓-日"
    },
    {
        "name": "【采购到货】采购入库监控",
        "url": "https://youdata.yx.netease.com/dash/folder/3352?rid=35201",
        "file": "PO批次SKU在途明细"
    }
]


def create_report_content(document_id: str, bu_name: str, date: str) -> str:
    """
    创建常用有数报表到飞书文档
    
    Args:
        document_id: 飞书文档id
        
    Returns:
        str: 创建的文档block_id或相关标识符
    """
    try:
        # 参数验证
        if not document_id or not isinstance(document_id, str):
            raise ValueError("document_id 必须是非空字符串")
        
        logger.info(f"开始创建常用有数报表内容，文档ID: {document_id}")
        
        # 创建DocClient实例
        docClient = get_doc_client()
        
        # 获取文档根块ID
        try:
            doc_info = docClient.get_document_content(document_id)
            root_block_id = doc_info["items"][0]["block_id"]
            # logger.info(f"获取文档根块ID成功: {root_block_id}")
        except Exception as e:
            logger.error(f"获取文档内容失败: {str(e)}")
            raise Exception(f"无法获取文档内容，请检查文档ID是否正确: {document_id}")
        
        # 步骤1：插入三级标题
        try:
            insert_heading3(docClient, document_id, root_block_id, "四、常用有数报表")
            # logger.info("三级标题 '四、常用有数报表' 插入成功")
        except Exception as e:
            logger.error(f"插入三级标题失败: {str(e)}")
            raise Exception(f"插入标题失败: {str(e)}")
        
        # 步骤2：循环插入有数报表列表
        logger.info(f"开始插入有数报表列表，共 {len(YOUDATA_REPORTS)} 个报表")
        
        inserted_count = 0
        failed_reports = []
        
        for index, report in enumerate(YOUDATA_REPORTS, 1):
            try:
                report_name = report.get("name", "")
                report_url = report.get("url", "")
                
                # 验证报表数据
                if not report_name or not report_url:
                    logger.warning(f"报表 {index} 数据不完整，跳过: name='{report_name}', url='{report_url}'")
                    failed_reports.append(f"报表 {index}: 数据不完整")
                    continue
                
                # 插入带超链接的有序列表项
                res = insert_ordered_list(
                    docClient, 
                    document_id, 
                    root_block_id, 
                    report_name, 
                    link_url=report_url
                )
                
                if report.get("file", None):
                  file_name = f"{report_name}-{bu_name}-{date}.csv"
                  # 提取BU数据
                  filter_csv_by_bu_name(
                    f"C:\\KPI监控\\{date}\\{report['file']}-{date}.csv",
                    "商品BU名称",
                    bu_name,
                    f"C:\\KPI监控\\{date}\\processed\\{file_name}"
                  )

                  # 检查文件大小，如果超过20MB则压缩
                  file_path = f"C:\\KPI监控\\{date}\\processed\\{file_name}"
                  file_size = os.path.getsize(file_path)
                  max_size = 20 * 1024 * 1024  # 20MB

                  if file_size > max_size:
                      logger.info(f"文件 {file_name} 大小为 {file_size / (1024*1024):.2f}MB，超过20MB限制，开始压缩...")

                      # 创建压缩文件
                      zip_file_name = file_name.replace('.csv', '.zip')
                      zip_file_path = f"C:\\KPI监控\\{date}\\processed\\{zip_file_name}"

                      with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                          zipf.write(file_path, file_name)

                      compressed_size = os.path.getsize(zip_file_path)
                      logger.info(f"压缩完成，压缩后大小为 {compressed_size / (1024*1024):.2f}MB")

                      # 使用压缩文件
                      final_file_path = zip_file_path
                      final_file_name = zip_file_name
                  else:
                      logger.info(f"文件 {file_name} 大小为 {file_size / (1024*1024):.2f}MB，无需压缩")
                      final_file_path = file_path
                      final_file_name = file_name

                  insert_file(
                      docClient,
                      document_id,
                      res['children'][0]['block_id'],
                      final_file_path,
                      file_name=final_file_name
                  )
                
                inserted_count += 1
                logger.info(f"报表 {index} 插入成功: {report_name}")
                
            except Exception as e:
                error_msg = f"报表 {index} 插入失败: {str(e)}"
                logger.error(error_msg)
                failed_reports.append(error_msg)
                continue
        
        # 记录插入结果
        logger.info(f"有数报表列表插入完成，成功: {inserted_count}/{len(YOUDATA_REPORTS)}")
        
        if failed_reports:
            logger.warning(f"部分报表插入失败:")
            for failed in failed_reports:
                logger.warning(f"  - {failed}")
        
        # 验证是否至少插入了一个报表
        if inserted_count == 0:
            raise Exception("所有报表都插入失败，请检查网络连接和权限设置")
        
        logger.info(f"常用有数报表内容创建完成，文档ID: {document_id}")
        return root_block_id
        
    except ValueError as e:
        logger.error(f"参数验证失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"创建常用有数报表内容失败: {str(e)}")
        raise e


def get_report_list():
    """
    获取有数报表列表
    
    Returns:
        list: 报表列表
    """
    return YOUDATA_REPORTS.copy()



def main(args=None):
    """
    测试函数
    
    Args:
        args: 命令行参数（可选）
    """
    try:
        # 测试参数
        test_document_id = "P3lNd2UWioGehExc4hLc9vD8nXb"  # 示例文档ID
        
        logger.info("开始测试常用有数报表功能")
        
        result = create_report_content(test_document_id, "大服配", "2025-08-04")
        logger.info(f"创建成功，返回block_id: {result}")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"测试失败: {str(e)}")