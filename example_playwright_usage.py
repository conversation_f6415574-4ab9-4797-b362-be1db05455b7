#!/usr/bin/env python3
"""
Playwright版本实际使用示例：展示如何在项目中使用chart_playwright.py生成各种图表
"""

import base64
import pandas as pd
import time
from .chart import generate_chart, ChartGeneratorError
from .logger import logger

def example_business_dashboard():
    """业务仪表板示例 - 使用Playwright版本"""
    logger.info("=== 业务仪表板示例 (Playwright版本) ===")
    
    # 季度业绩数据
    quarterly_data = [
        {"quarter": "Q1 2024", "revenue": 2800000, "profit": 420000},
        {"quarter": "Q2 2024", "revenue": 3200000, "profit": 480000},
        {"quarter": "Q3 2024", "revenue": 3600000, "profit": 540000},
        {"quarter": "Q4 2024", "revenue": 4000000, "profit": 600000}
    ]
    
    # 生成收入趋势图
    try:
        start_time = time.time()
        revenue_chart = generate_chart(
            quarterly_data,
            "line",
            {
                "title": "2024年季度收入趋势 (Playwright)",
                "width": 800,
                "height": 450,
                "x_field": "quarter",
                "y_field": "revenue",
                "line_color": "#1890ff",
                "line_width": 3
            }
        )
        generation_time = time.time() - start_time
        
        # 保存图表
        with open("playwright_revenue_trend.png", "wb") as f:
            f.write(base64.b64decode(revenue_chart))
        logger.info(f"✓ 收入趋势图已生成: playwright_revenue_trend.png (耗时: {generation_time:.2f}s)")
        
    except ChartGeneratorError as e:
        logger.info(f"✗ 收入趋势图生成失败: {e}")

def example_market_analysis():
    """市场分析示例 - 展示Playwright的性能优势"""
    logger.info("\n=== 市场分析示例 (Playwright版本) ===")
    
    # 市场细分数据
    market_segments = [
        {"segment": "企业客户", "share": 45, "growth": 12},
        {"segment": "中小企业", "share": 30, "growth": 18},
        {"segment": "个人用户", "share": 20, "growth": 25},
        {"segment": "政府机构", "share": 5, "growth": 8}
    ]
    
    # 生成市场份额饼图
    try:
        start_time = time.time()
        market_pie = generate_chart(
            market_segments,
            "pie",
            {
                "title": "市场细分份额分布 (Playwright)",
                "width": 650,
                "height": 650,
                "category_field": "segment",
                "value_field": "share"
            }
        )
        generation_time = time.time() - start_time
        
        with open("playwright_market_segments.png", "wb") as f:
            f.write(base64.b64decode(market_pie))
        logger.info(f"✓ 市场份额图已生成: playwright_market_segments.png (耗时: {generation_time:.2f}s)")
        
    except ChartGeneratorError as e:
        logger.info(f"✗ 市场份额图生成失败: {e}")

def example_performance_metrics():
    """性能指标示例 - 使用pandas DataFrame"""
    logger.info("\n=== 性能指标示例 (Playwright + pandas) ===")
    
    # 使用pandas创建性能数据
    performance_df = pd.DataFrame({
        'metric': ['响应时间', '吞吐量', '错误率', '可用性', 'CPU使用率', '内存使用率'],
        'current': [120, 850, 0.5, 99.9, 65, 70],
        'target': [100, 1000, 0.2, 99.95, 80, 85],
        'category': ['延迟', '性能', '质量', '可靠性', '资源', '资源']
    })
    
    # 生成当前性能柱状图
    try:
        start_time = time.time()
        performance_chart = generate_chart(
            performance_df[['metric', 'current']],
            "bar",
            {
                "title": "当前系统性能指标 (Playwright + pandas)",
                "width": 750,
                "height": 500,
                "x_field": "metric",
                "y_field": "current",
                "bar_color": "#52c41a"
            }
        )
        generation_time = time.time() - start_time
        
        with open("playwright_performance_metrics.png", "wb") as f:
            f.write(base64.b64decode(performance_chart))
        logger.info(f"✓ 性能指标图已生成: playwright_performance_metrics.png (耗时: {generation_time:.2f}s)")
        
    except ChartGeneratorError as e:
        logger.info(f"✗ 性能指标图生成失败: {e}")

def example_correlation_analysis():
    """相关性分析示例 - 散点图"""
    logger.info("\n=== 相关性分析示例 (Playwright版本) ===")
    
    # 用户活跃度与收入相关性数据
    correlation_data = [
        {"active_users": 1000, "revenue": 50000, "region": "北京", "month": "Jan"},
        {"active_users": 1200, "revenue": 58000, "region": "北京", "month": "Feb"},
        {"active_users": 1500, "revenue": 72000, "region": "北京", "month": "Mar"},
        {"active_users": 800, "revenue": 42000, "region": "上海", "month": "Jan"},
        {"active_users": 950, "revenue": 48000, "region": "上海", "month": "Feb"},
        {"active_users": 1100, "revenue": 55000, "region": "上海", "month": "Mar"},
        {"active_users": 600, "revenue": 32000, "region": "广州", "month": "Jan"},
        {"active_users": 750, "revenue": 38000, "region": "广州", "month": "Feb"},
        {"active_users": 900, "revenue": 45000, "region": "广州", "month": "Mar"},
        {"active_users": 500, "revenue": 28000, "region": "深圳", "month": "Jan"},
        {"active_users": 650, "revenue": 35000, "region": "深圳", "month": "Feb"},
        {"active_users": 800, "revenue": 42000, "region": "深圳", "month": "Mar"}
    ]
    
    # 生成相关性散点图
    try:
        start_time = time.time()
        correlation_chart = generate_chart(
            correlation_data,
            "scatter",
            {
                "title": "用户活跃度与收入相关性分析 (Playwright)",
                "width": 800,
                "height": 550,
                "x_field": "active_users",
                "y_field": "revenue",
                "color_field": "region",
                "point_size": 6
            }
        )
        generation_time = time.time() - start_time
        
        with open("playwright_correlation_analysis.png", "wb") as f:
            f.write(base64.b64decode(correlation_chart))
        logger.info(f"✓ 相关性分析图已生成: playwright_correlation_analysis.png (耗时: {generation_time:.2f}s)")
        
    except ChartGeneratorError as e:
        logger.info(f"✗ 相关性分析图生成失败: {e}")

def example_batch_generation():
    """批量生成示例 - 展示Playwright的效率"""
    logger.info("\n=== 批量生成示例 (Playwright版本) ===")
    
    # 模拟多个部门的数据
    departments = {
        "销售部": [
            {"month": "1月", "target": 100, "actual": 95},
            {"month": "2月", "target": 110, "actual": 108},
            {"month": "3月", "target": 120, "actual": 125}
        ],
        "市场部": [
            {"month": "1月", "target": 80, "actual": 85},
            {"month": "2月", "target": 85, "actual": 82},
            {"month": "3月", "target": 90, "actual": 95}
        ],
        "技术部": [
            {"month": "1月", "target": 60, "actual": 65},
            {"month": "2月", "target": 65, "actual": 70},
            {"month": "3月", "target": 70, "actual": 68}
        ]
    }
    
    total_start_time = time.time()
    generated_charts = 0
    
    for dept_name, dept_data in departments.items():
        try:
            start_time = time.time()
            chart = generate_chart(
                dept_data,
                "line",
                {
                    "title": f"{dept_name}月度目标达成情况 (Playwright)",
                    "width": 600,
                    "height": 400,
                    "x_field": "month",
                    "y_field": "actual",
                    "line_color": "#722ed1"
                }
            )
            generation_time = time.time() - start_time
            
            filename = f"playwright_{dept_name}_performance.png"
            with open(filename, "wb") as f:
                f.write(base64.b64decode(chart))
            
            logger.info(f"  ✓ {dept_name}图表已生成: {filename} (耗时: {generation_time:.2f}s)")
            generated_charts += 1
            
        except ChartGeneratorError as e:
            logger.info(f"  ✗ {dept_name}图表生成失败: {e}")
    
    total_time = time.time() - total_start_time
    logger.info(f"✓ 批量生成完成: {generated_charts}个图表，总耗时: {total_time:.2f}s")
    logger.info(f"✓ 平均每个图表耗时: {total_time/generated_charts:.2f}s")

def main(args):
    """主函数"""
    logger.info("开始使用Playwright版本生成业务图表示例...\n")
    
    # 记录总开始时间
    total_start = time.time()
    
    # 运行所有示例
    example_business_dashboard()
    example_market_analysis()
    example_performance_metrics()
    example_correlation_analysis()
    example_batch_generation()
    
    total_time = time.time() - total_start
    
    logger.info(f"\n=== 总结 (Playwright版本) ===")
    logger.info(f"所有示例图表已生成完成！总耗时: {total_time:.2f}s")
    logger.info("生成的文件：")
    logger.info("- playwright_revenue_trend.png (收入趋势)")
    logger.info("- playwright_market_segments.png (市场份额)")
    logger.info("- playwright_performance_metrics.png (性能指标)")
    logger.info("- playwright_correlation_analysis.png (相关性分析)")
    logger.info("- playwright_销售部_performance.png (销售部门)")
    logger.info("- playwright_市场部_performance.png (市场部门)")
    logger.info("- playwright_技术部_performance.png (技术部门)")
    logger.info(f"\n🚀 Playwright版本优势：更快的渲染速度，更好的稳定性！")

if __name__ == "__main__":
    main()
