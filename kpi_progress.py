import os
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from .utils import extract_csv_columns, reorganize_csv_data, extract_top_n, convert_to_numeric, calculate_period_comparison, parse_date_column, format_date_to_MMDD
from .logger import logger
from .const import KPI_CONFIG_LIST
from .package import variables as glv   


def _extract_all_configs_from_kpi_list() -> List[Dict[str, Any]]:
    """
    从KPI_CONFIG_LIST中提取所有配置项（line_chart、detail_table、top_table）
    转换为与原config_list兼容的格式

    Returns:
        List[Dict[str, Any]]: 配置项列表
    """
    all_configs = []

    for kpi_config in KPI_CONFIG_LIST:
        # 添加line_chart配置
        if "line_chart" in kpi_config:
            line_chart = kpi_config["line_chart"]
            line_chart_config = {
                "name": line_chart["name"],
                "type": "lineChart",
                "columns": line_chart["columns"]
            }
            all_configs.append(line_chart_config)

        # 添加detail_table配置
        if "detail_table" in kpi_config:
            detail_table = kpi_config["detail_table"]
            detail_table_config = {
                "name": detail_table["name"],
                "type": "detailTable",
                "date_column": detail_table["date_column"],
                "bu_column": detail_table["bu_column"],
                "add_total_column": detail_table["add_total_column"]
            }
            all_configs.append(detail_table_config)

        # 添加top_table配置（如果存在）
        if "top_table" in kpi_config:
            top_table = kpi_config["top_table"]
            top_table_config = {
                "name": top_table["name"],
                "type": "topTable",
                "bu_column": top_table["bu_column"],
                "top_n": top_table["top_n"]
            }
            all_configs.append(top_table_config)

    return all_configs


def process_kpi_data(folder: str, date: str) -> None:
    """
    遍历config_list列表，针对每一项做对应的处理
    
    Args:
        folder (str): 数据文件夹路径
        date (str): 日期字符串，格式如 "2024-01-01"
    
    Returns:
        None
    """
    
    # 确保processed文件夹存在
    processed_folder = os.path.join(folder, "processed")
    if not os.path.exists(processed_folder):
        os.makedirs(processed_folder)
        # logger.info(f"创建processed文件夹: {processed_folder}")
    
    # 从KPI_CONFIG_LIST中提取所有配置项
    all_configs = _extract_all_configs_from_kpi_list()

    logger.info(f"开始处理KPI数据，文件夹: {folder}, 日期: {date}")
    logger.info(f"配置项总数: {len(all_configs)}")
    logger.info("-" * 50)

    success_count = 0
    error_count = 0

    for i, config in enumerate(all_configs, 1):
        try:
            name = config.get("name")
            config_type = config.get("type")

            logger.info(f"[{i}/{len(all_configs)}] 处理配置项: {name}")
            logger.info(f"类型: {config_type}")
            
            # 构建输入和输出文件路径
            input_csv_path = os.path.join(folder, f"{name}-{date}.csv")
            output_csv_path = os.path.join(processed_folder, f"{name}-{date}.csv")
            
            # 检查输入文件是否存在
            if not os.path.exists(input_csv_path):
                logger.error(f"警告: 输入文件不存在: {input_csv_path}")
                error_count += 1
                continue
            
            # 根据类型调用相应的处理方法
            if config_type == "lineChart":
                result = _process_line_chart(config, input_csv_path, output_csv_path)
            elif config_type == "detailTable":
                result = _process_detail_table(config, input_csv_path, output_csv_path)
            elif config_type == "topTable":
                result = _process_top_table(config, input_csv_path, output_csv_path)
            else:
                logger.error(f"错误: 未知的配置类型: {config_type}")
                error_count += 1
                continue
            
            if result is not None:
                logger.info(f"✓ 处理成功: {name}")
                success_count += 1
            else:
                logger.error(f"✗ 处理失败: {name}")
                error_count += 1
                
        except Exception as e:
            logger.error(f"✗ 处理配置项时发生错误: {e}")
            error_count += 1
        
        logger.info("-" * 30)
    
    # 输出处理结果统计
    logger.info("=" * 50)
    logger.info(f"处理完成! 成功: {success_count}, 失败: {error_count}")
    logger.info("=" * 50)


def _process_line_chart(config: Dict[str, Any], input_csv_path: str, output_csv_path: str):
    """
    处理折线图类型的配置
    
    Args:
        config: 配置字典
        input_csv_path: 输入文件路径
        output_csv_path: 输出文件路径
    
    Returns:
        处理结果DataFrame或None
    """
    columns = config.get("columns")
    if not columns:
        logger.info("错误: lineChart配置缺少columns参数")
        return None
    
    # logger.info(f"调用extract_csv_columns方法")
    # logger.info(f"输入文件: {input_csv_path}")
    # logger.info(f"输出文件: {output_csv_path}")
    # logger.info(f"提取列: {columns}")
    
    return extract_csv_columns(
        input_csv_path=input_csv_path,
        output_csv_path=output_csv_path,
        columns=columns
    )


def _process_detail_table(config: Dict[str, Any], input_csv_path: str, output_csv_path: str):
    """
    处理明细表格类型的配置
    
    Args:
        config: 配置字典
        input_csv_path: 输入文件路径
        output_csv_path: 输出文件路径
    
    Returns:
        处理结果DataFrame或None
    """
    date_column = config.get("date_column")
    bu_column = config.get("bu_column")
    add_total_column = config.get("add_total_column", True)
    
    if not all([date_column, bu_column]):
        logger.info("错误: detailTable配置缺少必要参数 (date_column, bu_column)")
        return None
    
    # logger.info(f"调用reorganize_csv_data方法")
    # logger.info(f"输入文件: {input_csv_path}")
    # logger.info(f"输出文件: {output_csv_path}")
    # logger.info(f"日期列: {date_column}")
    # logger.info(f"BU列: {bu_column}")
    # logger.info(f"添加总计列: {add_total_column}")
    
    return reorganize_csv_data(
        input_csv_path=input_csv_path,
        date_column=date_column,
        bu_column=bu_column,
        output_csv_path=output_csv_path,
        add_total_column=add_total_column
    )


def _process_top_table(config: Dict[str, Any], input_csv_path: str, output_csv_path: str):
    """
    处理TOP表格类型的配置
    
    Args:
        config: 配置字典
        input_csv_path: 输入文件路径
        output_csv_path: 输出文件路径
    
    Returns:
        处理结果DataFrame或None
    """
    bu_column = config.get("bu_column")
    top_n = config.get("top_n", 3)  # 默认值为3
    
    if not bu_column:
        logger.info("错误: topTable配置缺少bu_column参数")
        return None
    
    # logger.info(f"调用extract_top_n方法")
    # logger.info(f"输入文件: {input_csv_path}")
    # logger.info(f"输出文件: {output_csv_path}")
    # logger.info(f"BU列: {bu_column}")
    # logger.info(f"TOP数量: {top_n}")
    
    return extract_top_n(
        input_csv_path=input_csv_path,
        bu_column=bu_column,
        output_csv_path=output_csv_path,
        top_n=top_n
    )

def extract_kpi_summary_data(folder: str, date: str, output_csv_path: str) -> Optional[pd.DataFrame]:
    """
    提取给定csv文件中的指定数据，并合并成一个新的csv文件

    遍历KPI_CONFIG_LIST，对每一个指标，打开detail_table.name对应的文件，
    提取全部BU各个日期的指标数值，指标列名为kpi_column

    Args:
        folder (str): 数据文件夹路径
        date (str): 日期字符串，格式如 "2024-01-01"
        output_csv_path (str): 输出CSV文件路径

    Returns:
        Optional[pd.DataFrame]: 合并后的DataFrame，如果处理失败返回None
    """
    logger.info(f"开始提取KPI汇总数据，日期: {date}")

    # 确保processed文件夹存在
    processed_folder = os.path.join(folder, "processed")
    if not os.path.exists(processed_folder):
        logger.error(f"processed文件夹不存在: {processed_folder}")
        return None

    all_kpi_data = []

    for i, kpi_config in enumerate(KPI_CONFIG_LIST, 1):
        try:
            logger.info(f"[{i}/{len(KPI_CONFIG_LIST)}] 处理KPI: {kpi_config['title']}")

            # 获取配置信息
            kpi_name = kpi_config.get('kpi_name', '')
            kpi_column = kpi_config.get('kpi_column', '')
            detail_table_config = kpi_config.get('detail_table', {})

            if not detail_table_config:
                logger.warning(f"KPI配置缺少detail_table: {kpi_config['title']}")
                continue

            detail_table_name = detail_table_config.get('name', '')
            bu_column = detail_table_config.get('bu_column', '商品BU名称')
            date_column = detail_table_config.get('date_column', '')

            if not all([kpi_name, kpi_column, detail_table_name, date_column]):
                logger.warning(f"KPI配置信息不完整: {kpi_config['title']}")
                continue

            # 构建文件路径
            input_csv_path = os.path.join(folder, f"{detail_table_name}-{date}.csv")

            if not os.path.exists(input_csv_path):
                logger.warning(f"文件不存在: {input_csv_path}")
                continue

            # 读取CSV文件
            df = pd.read_csv(input_csv_path, encoding='utf-8')
            # logger.info(f"读取文件成功: {input_csv_path}, 数据形状: {df.shape}")

            # 验证必需列是否存在
            required_columns = [bu_column, date_column, kpi_column]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.warning(f"文件缺少必需列 {missing_columns}: {input_csv_path}")
                continue

            # 提取KPI数据（处理全部BU的数据）
            kpi_row_data_list = _extract_single_kpi_data(df, bu_column, kpi_name, kpi_column, date_column)
            if kpi_row_data_list:
                all_kpi_data.extend(kpi_row_data_list)  # 使用extend而不是append，因为返回的是列表
                logger.info(f"✓ 成功提取KPI数据: {kpi_name}, 包含 {len(kpi_row_data_list)} 个BU")
            else:
                logger.warning(f"✗ 提取KPI数据失败: {kpi_name}")

        except Exception as e:
            logger.error(f"处理KPI时发生错误 {kpi_config.get('title', 'Unknown')}: {str(e)}")
            continue

    if not all_kpi_data:
        logger.error("没有成功提取任何KPI数据")
        return None

    # 创建汇总DataFrame
    summary_df = pd.DataFrame(all_kpi_data)

    # 重新排列列顺序：固定列 + 按时间排序的日期列 + 环比同比列
    if not summary_df.empty:
        summary_df = _reorder_summary_columns(summary_df)

    # 确保输出目录存在
    output_dir = os.path.dirname(output_csv_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存到CSV文件
    summary_df.to_csv(output_csv_path, index=False, encoding='utf-8')
    logger.info(f"KPI汇总数据已保存到: {output_csv_path}")
    logger.info(f"汇总数据形状: {summary_df.shape}")

    return summary_df


def _filter_t1_to_t8_data(bu_data_sorted: pd.DataFrame, date_column: str, kpi_name: str) -> pd.DataFrame:
    """
    筛选T-1到T-8的数据（连续的前8天）

    Args:
        bu_data_sorted: 已按日期排序的BU数据
        date_column: 日期列名
        kpi_name: KPI名称（用于日志）

    Returns:
        筛选后的DataFrame，包含T-1到T-8（连续前8天）的数据
    """
    if bu_data_sorted.empty:
        return bu_data_sorted

    try:
        # 获取最新日期作为T-1
        # latest_date_str = bu_data_sorted[date_column].iloc[-1]
        latest_date = parse_date_column(glv['target_date'])

        # if pd.isna(latest_date):
        #     logger.warning(f"无法解析最新日期 '{latest_date_str}'，使用原始数据")
        #     return bu_data_sorted

        # 计算T-1到T-8的日期范围（连续的8天）
        t1_date = latest_date
        t8_date = latest_date - timedelta(days=7)  # T-8是T-1往前推7天

        # logger.info(f"KPI '{kpi_name}' T-1日期: {t1_date.strftime('%Y-%m-%d')}")
        # logger.info(f"KPI '{kpi_name}' T-8日期: {t8_date.strftime('%Y-%m-%d')}")

        # 筛选在T-8到T-1日期范围内的数据
        filtered_rows = []
        for _, row in bu_data_sorted.iterrows():
            row_date = parse_date_column(row[date_column])
            if pd.notna(row_date) and t8_date <= row_date <= t1_date:
                filtered_rows.append(row)

        if filtered_rows:
            filtered_data = pd.DataFrame(filtered_rows)
            # logger.info(f"KPI '{kpi_name}' 在T-1到T-8日期范围内找到 {len(filtered_data)} 条数据")

            # 显示实际包含的日期
            actual_dates = sorted(filtered_data[date_column].unique())
            # logger.info(f"KPI '{kpi_name}' 实际包含的日期: {actual_dates}")

            return filtered_data
        else:
            logger.warning(f"KPI '{kpi_name}' 在T-1到T-8日期范围内未找到数据，使用原始数据")
            return bu_data_sorted

    except Exception as e:
        logger.warning(f"T-1到T-8数据筛选失败，使用原始数据: {e}")
        return bu_data_sorted


def _reorder_summary_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    重新排列汇总数据的列顺序，并格式化日期列标题

    Args:
        df: 原始汇总DataFrame

    Returns:
        重新排列列顺序并格式化日期列标题后的DataFrame
    """
    # 固定列
    fixed_columns = ['BU', '指标', '三分值']

    # 结尾列
    end_columns = ['环比变化', '同比变化']

    # 获取所有日期列
    all_columns = df.columns.tolist()
    date_columns = [col for col in all_columns if col not in fixed_columns + end_columns]

    # 对日期列进行排序并创建格式化映射
    column_rename_mapping = {}
    try:
        date_columns_with_parsed = [(col, parse_date_column(col)) for col in date_columns]
        valid_date_columns = [(col, parsed_date) for col, parsed_date in date_columns_with_parsed if pd.notna(parsed_date)]
        valid_date_columns.sort(key=lambda x: x[1])

        # 创建格式化后的日期列名
        date_columns_sorted = []
        for col, parsed_date in valid_date_columns:
            try:
                # 使用format_date_to_MMDD格式化日期列标题
                formatted_date = format_date_to_MMDD(parsed_date.strftime('%Y-%m-%d'))
                column_rename_mapping[col] = formatted_date
                date_columns_sorted.append(formatted_date)
                # logger.info(f"日期列 '{col}' 格式化为 '{formatted_date}'")
            except Exception as e:
                logger.warning(f"日期列 '{col}' 格式化失败，保持原名: {e}")
                date_columns_sorted.append(col)

        # 添加无法解析的日期列到末尾（保持原名）
        invalid_date_columns = [col for col, parsed_date in date_columns_with_parsed if pd.isna(parsed_date)]
        date_columns_sorted.extend(invalid_date_columns)

    except Exception as e:
        logger.warning(f"日期列排序失败，使用原始顺序: {e}")
        date_columns_sorted = date_columns

    # 重命名日期列
    if column_rename_mapping:
        df = df.rename(columns=column_rename_mapping)
        # logger.info(f"重命名日期列: {column_rename_mapping}")

    # 重新排列列顺序
    new_column_order = fixed_columns + date_columns_sorted + end_columns

    # 只选择存在的列
    existing_columns = [col for col in new_column_order if col in df.columns]

    return df[existing_columns]


def _extract_single_kpi_data(data: pd.DataFrame, bu_column: str, kpi_name: str,
                           kpi_column: str, date_column: str) -> Optional[List[Dict]]:
    """
    提取单个KPI的全部BU数据

    特殊处理规则：
    - 销量计划达成率：只保留最新1条数据，日期修改为昨天，不计算同环比
    - 其他KPI：保留T-1到T-8（连续前8天）的数据，正常计算同环比

    Args:
        data: 包含全部BU的数据
        bu_column: BU列名
        kpi_name: KPI名称
        kpi_column: KPI列名
        date_column: 日期列名

    Returns:
        包含全部BU的KPI数据列表，如果提取失败返回None
    """
    try:
        # 验证必需列是否存在
        required_columns = [bu_column, date_column, kpi_column]
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            logger.error(f"数据缺少必需列 {missing_columns}")
            return None

        # 获取所有唯一的BU
        unique_bus = data[bu_column].unique()
        logger.info(f"KPI '{kpi_name}' 找到 {len(unique_bus)} 个BU: {list(unique_bus)}")

        all_bu_results = []

        # 为每个BU处理数据
        for bu_name in unique_bus:
            logger.info(f"处理BU: {bu_name}")

            # 筛选当前BU的数据
            bu_data = data[data[bu_column] == bu_name].copy()
            if bu_data.empty:
                logger.warning(f"BU '{bu_name}' 没有数据")
                continue

            # 按日期排序
            bu_data_sorted = bu_data.copy()

            # 解析日期并排序
            try:
                bu_data_sorted['parsed_date'] = bu_data_sorted[date_column].apply(parse_date_column)
                bu_data_sorted = bu_data_sorted.sort_values('parsed_date')
                bu_data_sorted = bu_data_sorted.drop('parsed_date', axis=1)
            except Exception as e:
                logger.warning(f"BU '{bu_name}' 日期排序失败，使用原始顺序: {e}")

            # 特殊处理：销量计划达成率只保留最新的1条数据
            if kpi_name == "计划达成率":
                if len(bu_data_sorted) > 1:
                    bu_data_sorted = bu_data_sorted.tail(1)
                    # logger.info(f"BU '{bu_name}' KPI '{kpi_name}' 只保留最新的1条数据")
            else:
                # 其他KPI保留T-1到T-8时间点的数据
                bu_data_sorted = _filter_t1_to_t8_data(bu_data_sorted, date_column, f"{bu_name}-{kpi_name}")

            # 创建基础行数据
            row_data = {
                'BU': bu_name,
                '指标': kpi_name,
                '三分值': glv['kpi_target_dict'].get(f"{kpi_name}_{bu_name}", '')
            }

            # 添加各日期的数据
            date_values = {}
            for _, row in bu_data_sorted.iterrows():
                if kpi_name == "计划达成率":
                    # 销量计划达成率：将日期修改为T-1
                    date_val = parse_date_column(glv['target_date'])
                    # logger.info(f"BU '{bu_name}' 销量计划达成率日期修改为T-1: {date_val}")
                else:
                    # 其他KPI：使用原始日期
                    date_val = parse_date_column(row[date_column])

                kpi_val = row[kpi_column]
                date_values[str(date_val)] = kpi_val
                row_data[str(date_val)] = kpi_val

            # 计算环比和同比变化
            if kpi_name == "计划达成率":
                # 销量计划达成率不需要计算同环比
                row_data['环比变化'] = ''
                row_data['同比变化'] = ''
                # logger.info(f"BU '{bu_name}' 销量计划达成率跳过同环比计算")
            elif len(bu_data_sorted) >= 2:
                # 其他KPI计算同环比变化
                # 获取T-1, T-2, T-8的数值
                t1_value = None
                t2_value = None
                t8_value = None

                # 计算T-1, T-2, T-8对应的日期
                target_date = parse_date_column(glv['target_date'])
                if pd.notna(target_date):
                    t1_date = str(target_date)  # T-1是目标日期
                    t2_date = str(target_date - timedelta(days=1))  # T-2是T-1前一天
                    t8_date = str(target_date - timedelta(days=7))  # T-8是T-1前7天

                    # 从date_values中获取对应日期的数值
                    if t1_date in date_values:
                        t1_value = convert_to_numeric(date_values[t1_date])
                    if t2_date in date_values:
                        t2_value = convert_to_numeric(date_values[t2_date])
                    if t8_date in date_values:
                        t8_value = convert_to_numeric(date_values[t8_date])

                    # logger.info(f"BU '{bu_name}' KPI '{kpi_name}' T-1日期: {t1_date}, 值: {t1_value}")
                    # logger.info(f"BU '{bu_name}' KPI '{kpi_name}' T-2日期: {t2_date}, 值: {t2_value}")
                    # logger.info(f"BU '{bu_name}' KPI '{kpi_name}' T-8日期: {t8_date}, 值: {t8_value}")

                # 计算同环比变化
                chain_ratio, year_on_year = calculate_period_comparison(
                    t1_value, t2_value, t8_value, kpi_name
                )
                row_data['环比变化'] = chain_ratio
                row_data['同比变化'] = year_on_year
            else:
                row_data['环比变化'] = 'N/A'
                row_data['同比变化'] = 'N/A'

            # 将当前BU的结果添加到列表中
            all_bu_results.append(row_data)
            logger.info(f"✓ 成功提取BU '{bu_name}' 的KPI数据: {kpi_name}")

        logger.info(f"KPI '{kpi_name}' 总共提取了 {len(all_bu_results)} 个BU的数据")
        return all_bu_results

    except Exception as e:
        logger.error(f"提取单个KPI数据失败 {kpi_name}: {str(e)}")
        return None


def main(args):
    # 示例用法
    folder = "C:\\KPI监控\\2025-07-31"  # 数据文件夹路径
    date = "2025-07-31"  # 日期

    #process_kpi_data(folder, date)

    # 测试KPI汇总数据提取
    output_path = os.path.join(folder, "processed", f"指标达成汇总-全部BU-{date}.csv")
    extract_kpi_summary_data(folder, date, output_path)